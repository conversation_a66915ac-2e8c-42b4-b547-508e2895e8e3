import smtplib
import imaplib
import ssl
import os
import email
from typing import List, Optional, Dict, Tuple
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from email.mime.base import MIMEBase
from email import encoders
from email.utils import formatdate, parsedate_to_datetime

from core.common.utils.logHelper import logger


class EmailManager:
    def __init__(self, username: str, auth_code: str):
        """
        邮件管理器（支持发送和接收）
        :param username: 邮箱地址（如**********）
        :param auth_code: SMTP/IMAP授权码（非登录密码）
        """
        self.username = username
        self.auth_code = auth_code
        self.domain = username.split('@')[-1]  # 提取邮箱域名（如qq.com）

        # 自动适配邮箱服务器（支持QQ、163、Gmail等）
        self.smtp_config = self._get_smtp_config()
        self.imap_config = self._get_imap_config()

        # 连接对象
        self.smtp = None
        self.imap = None

    # ------------------------------
    # 配置自动适配
    # ------------------------------
    def _get_smtp_config(self) -> Dict:
        """获取SMTP服务器配置"""
        configs = {
            'qq.com': {'server': 'smtp.qq.com', 'port': 465},
            '163.com': {'server': 'smtp.163.com', 'port': 465},
            '126.com': {'server': 'smtp.126.com', 'port': 465},
            'gmail.com': {'server': 'smtp.gmail.com', 'port': 465},
            'outlook.com': {'server': 'smtp.office365.com', 'port': 587}
        }
        return configs.get(self.domain, {'server': f'smtp.{self.domain}', 'port': 465})

    def _get_imap_config(self) -> Dict:
        """获取IMAP服务器配置（用于接收邮件）"""
        configs = {
            'qq.com': {'server': 'imap.qq.com', 'port': 993},
            '163.com': {'server': 'imap.163.com', 'port': 993},
            '126.com': {'server': 'imap.126.com', 'port': 993},
            'gmail.com': {'server': 'imap.gmail.com', 'port': 993},
            'outlook.com': {'server': 'imap.office365.com', 'port': 993}
        }
        return configs.get(self.domain, {'server': f'imap.{self.domain}', 'port': 993})

    # ------------------------------
    # 发送邮件相关
    # ------------------------------
    def _smtp_connect(self) -> bool:
        """建立SMTP连接（用于发送）"""
        self._smtp_disconnect()
        try:
            # 基于SSL的连接
            self.smtp = smtplib.SMTP_SSL(
                self.smtp_config['server'],
                self.smtp_config['port'],
                context=ssl.create_default_context(),
                timeout=15
            )
            self.smtp.ehlo()  # 执行握手
            self.smtp.login(self.username, self.auth_code)
            return True
        except Exception as e:
            print(f"SMTP连接失败: {e}")
            self.smtp = None
            return False

    def _smtp_disconnect(self):
        """关闭SMTP连接"""
        if self.smtp:
            try:
                self.smtp.quit()
            except:
                self.smtp.close()
            finally:
                self.smtp = None

    def send_email(self,
                   to_addrs: List[str],
                   subject: str,
                   content_text: Optional[str] = None,
                   content_html: Optional[str] = None,
                   attachments: Optional[List[str]] = None) -> bool:
        """发送邮件（同之前的实现，保持兼容）"""
        if not to_addrs:
            print("收件人列表不能为空")
            return False

        # 构建邮件
        msg = MIMEMultipart()
        msg["From"] = self.username
        msg["To"] = ", ".join(to_addrs)
        msg["Subject"] = subject
        msg["Date"] = formatdate(localtime=True)

        if content_text:
            msg.attach(MIMEText(content_text, "plain", "utf-8"))
        if content_html:
            msg.attach(MIMEText(content_html, "html", "utf-8"))

        # 添加附件
        if attachments:
            for file in attachments:
                if not os.path.isfile(file):
                    print(f"附件不存在: {file}")
                    continue
                try:
                    with open(file, "rb") as f:
                        part = MIMEBase("application", "octet-stream")
                        part.set_payload(f.read())
                    encoders.encode_base64(part)
                    filename = os.path.basename(file)
                    part.add_header(
                        "Content-Disposition",
                        f'attachment; filename*=UTF-8\'\'{filename}',
                    )
                    msg.attach(part)
                except Exception as e:
                    print(f"附件处理失败: {e}")

        # 发送重试
        max_retries = 2
        for attempt in range(max_retries + 1):
            try:
                if not self.smtp and not self._smtp_connect():
                    continue

                self.smtp.sendmail(self.username, to_addrs, msg.as_string())
                print(f"邮件发送成功: {to_addrs}")
                self._smtp_disconnect()
                return True
            except Exception as e:
                print(f"第{attempt+1}次发送失败: {e}")
                self._smtp_disconnect()

        print("所有发送尝试失败")
        return False

    # ------------------------------
    # 接收邮件相关（新增功能）
    # ------------------------------
    def _imap_connect(self) -> bool:
        """建立IMAP连接（用于接收邮件）"""
        self._imap_disconnect()
        try:
            # IMAP通常使用993端口+SSL
            self.imap = imaplib.IMAP4_SSL(
                self.imap_config['server'],
                self.imap_config['port'],
                timeout=15
            )
            # 登录验证
            self.imap.login(self.username, self.auth_code)
            return True
        except Exception as e:
            print(f"IMAP连接失败: {e}")
            self.imap = None
            return False

    def _imap_disconnect(self):
        """关闭IMAP连接"""
        if self.imap:
            try:
                self.imap.logout()
            except:
                pass
            finally:
                self.imap = None

    def list_mailboxes(self) -> List[Tuple[str, str]]:
        """获取邮箱中的文件夹列表（如收件箱、草稿箱等）"""
        if not self._imap_connect():
            return []

        try:
            status, data = self.imap.list()
            if status != 'OK':
                print("获取文件夹失败")
                return []

            # 解析文件夹信息（格式: (标志, 分隔符, 名称)）
            mailboxes = []
            for item in data:
                parts = item.decode().split(' "/" ')
                if len(parts) == 2:
                    mailboxes.append((parts[0].strip(), parts[1].strip()))
            return mailboxes
        finally:
            self._imap_disconnect()

    def receive_emails(self,
                       mailbox: str = "INBOX",  # 默认为收件箱
                       limit: int = 10,  # 最多获取多少封
                       criteria: str = "ALL"  # 筛选条件（如"UNSEEN"表示未读）
                       ) -> List[Dict]:
        """
        接收邮件
        :param mailbox: 邮箱文件夹（如"INBOX"收件箱）
        :param limit: 最大获取数量
        :param criteria: 筛选条件（ALL: 所有, UNSEEN: 未读, SEEN: 已读）
        :return: 邮件列表（包含主题、发件人、时间、内容等）
        """
        if not self._imap_connect():
            return []
        try:
            # 选择文件夹
            status, _ = self.imap.select(mailbox, readonly=True)  # readonly=True避免误操作
            if status != 'OK':
                print(f"无法打开文件夹: {mailbox}")
                return []

            # 搜索符合条件的邮件（返回邮件ID列表）
            status, email_ids = self.imap.search(None, criteria)
            if status != 'OK':
                print("搜索邮件失败")
                return []

            # 处理邮件ID（倒序排列，最新的在前）
            ids = email_ids[0].split()
            ids = list(reversed(ids))[:limit]  # 取最新的limit封
            if not ids:
                print("没有符合条件的邮件")
                return []

            emails = []
            for email_id in ids:
                # 获取邮件内容
                status, msg_data = self.imap.fetch(email_id, "(RFC822)")  # RFC822格式
                if status != 'OK':
                    print(f"获取邮件{email_id}失败")
                    continue

                # 解析邮件
                msg = email.message_from_bytes(msg_data[0][1])
                for item in msg.items():
                    logger.debug(item)

                email_info = {
                    'id': email_id.decode(),
                    'subject': email.header.decode_header(msg["Subject"])[0][0],  # 解码主题
                    'from': msg.get("From", ""),
                    'to': msg.get("To", ""),
                    'date': parsedate_to_datetime(msg["Date"]).isoformat() if msg["Date"] else None,
                    'is_read': criteria == "SEEN" or (criteria == "ALL" and 'SEEN' in msg.get_flags()),
                    'content_text': "",
                    'content_html': "",
                    'attachments': []
                }

                # 解码主题（处理中文）
                if isinstance(email_info['subject'], bytes):
                    email_info['subject'] = email_info['subject'].decode('utf-8', errors='replace')

                # 提取邮件内容和附件
                for part in msg.walk():
                    content_type = part.get_content_type()
                    disposition = part.get("Content-Disposition", "")

                    # 处理附件
                    if "attachment" in disposition:
                        filename = part.get_filename()
                        if filename:
                            # 解码附件名
                            filename = email.header.decode_header(filename)[0][0]
                            if isinstance(filename, bytes):
                                filename = filename.decode('utf-8', errors='replace')
                            email_info['attachments'].append(filename)

                    # 处理文本内容
                    elif content_type == "text/plain" and "attachment" not in disposition:
                        charset = part.get_content_charset() or 'utf-8'
                        try:
                            email_info['content_text'] += part.get_payload(decode=True).decode(charset, errors='replace')
                        except:
                            email_info['content_text'] += part.get_payload(decode=True).decode('utf-8', errors='replace')

                    # 处理HTML内容
                    elif content_type == "text/html" and "attachment" not in disposition:
                        charset = part.get_content_charset() or 'utf-8'
                        try:
                            email_info['content_html'] += part.get_payload(decode=True).decode(charset, errors='replace')
                        except:
                            email_info['content_html'] += part.get_payload(decode=True).decode('utf-8', errors='replace')

                emails.append(email_info)

            return emails

        finally:
            self._imap_disconnect()

    def mark_as_read(self, email_id: str, mailbox: str = "INBOX") -> bool:
        """将邮件标记为已读"""
        if not self._imap_connect():
            return False

        try:
            status, _ = self.imap.select(mailbox)
            if status != 'OK':
                return False

            # 添加SEEN标记（已读）
            self.imap.store(email_id, '+FLAGS', '\\Seen')
            return True
        except Exception as e:
            print(f"标记已读失败: {e}")
            return False
        finally:
            self._imap_disconnect()


# 使用示例
if __name__ == "__main__":
    # 配置邮箱信息（替换为你的）
    EMAIL = "<EMAIL>"
    AUTH_CODE = "sjyjwenivlaabhhe"  # 注意：需同时开启SMTP和IMAP服务的授权码

    # 初始化管理器
    email_manager = EmailManager(EMAIL, AUTH_CODE)

    # 1. 发送邮件（同之前用法）
    # email_manager.send_email(
    #     to_addrs=["<EMAIL>"],
    #     subject="测试发送功能",
    #     content_text="这是一封测试邮件"
    # )

    # 2. 接收邮件（新增功能）
    print("\n===== 邮箱文件夹列表 =====")
    mailboxes = email_manager.list_mailboxes()
    for mb in mailboxes:
        print(f"标志: {mb[0]}, 名称: {mb[1]}")

    print("\n===== 接收最新5封未读邮件 =====")
    received_emails = email_manager.receive_emails(
        mailbox="INBOX",  # 收件箱
        limit=2,
        criteria="UNSEEN"  # 只获取未读
    )
    # logger.debug(received_emails)
    for idx, mail in enumerate(received_emails, 1):
        # logger.debug(idx)
        logger.debug(mail)
        print(f"\n----- 邮件 {idx} -----")
        print(f"主题: {mail['subject']}")
        print(f"发件人: {mail['from']}")
        print(f"时间: {mail['date']}")
        print(f"附件数量: {len(mail['attachments'])}")
        print(f"预览内容: {mail['content_text']}")
        print(f"预览内容: {mail['content_html'][:100]}...")

        # 标记为已读
        # email_manager.mark_as_read(mail['id'])