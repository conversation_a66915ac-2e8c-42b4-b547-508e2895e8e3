import os.path
from typing import Optional
from pydantic import BaseModel, Field
from starlette.types import Lifespan
from core.common.utils.logHelper import logger
from core.common.utils.yamlHelper import yaml_to_object


class ConfigBase(BaseModel):
    env: str = 'dev'

class Node(BaseModel):
    host:str = ''
    port:int = 0

    def to_dict(self):
        return {
            "host": self.host,
            "port": self.port
        }

class Cluster(BaseModel):
    nodes: list = []
    def get_nodes(self):
        nds = []
        for n in self.nodes:
            nd = Node()
            nd.host = n.split(':')[0]
            nd.port = n.split(':')[1]
            nds.append(nd.to_dict())
        return nds

class Pool(BaseModel):
    # 连接池最大连接数（使用负值表示没有限制）
    max_connections:int = 200000
    # 连接池中的最大空闲连接
    max_idle: int = 10
    # 连接池最大阻塞等待时间（使用负值表示没有限制）
    max_wait: int = -1
    # 连接池中的最小空闲连接
    min_idle: int = 0

class RedisConfig(BaseModel):
    """
    RedisConfig Redis配置类
    用于配置Redis连接参数，包括主机地址、端口、认证信息、数据库选择和连接池设置
    Attributes:
        host (str): Redis服务器主机地址，默认为'redis'
        port (int): Redis服务器端口号，默认为6379
        username (Optional[str]): Redis用户名，用于Redis 6.0+的ACL认证，默认为'root'
        password (str): Redis密码，用于AUTH认证，默认为空字符串
        database (int): Redis数据库编号，范围0-15，默认为0

    Version: 1.2
    Date: 2020-02-11
    Updated: 2024-01-01 (添加类型注解和详细注释)
    """
    # Redis服务器配置
    host: str = None  # Redis服务器主机地址
    port: int = None     # Redis服务器端口号

    # 认证配置
    username: Optional[str] = 'root'  # Redis用户名（Redis 6.0+ ACL功能）
    password: str = ''                # Redis密码（AUTH认证）

    # 数据库配置
    database: int = 0  # Redis数据库编号（0-15）

    # 连接池配置
    pool: Pool = None
    cluster: Cluster = None

# 配置文件：FastapiConfig
class FastapiConfig(BaseModel):
    """FastAPI应用配置类

    注意：这个类使用类属性而不是实例属性，以便与现有代码兼容
    现有代码使用 FastapiConfig.__dict__ 和 FastapiConfig.title 等方式访问
    """

    # 基础配置
    debug: bool = True
    title: str = 'FastAPI Service'
    description: str = '基于FastAPI的服务工程'
    version: str = '0.0.0.0'
    host: str = '127.0.0.1'
    port: int = 8001

    # OpenAPI配置
    openapi_url: str = '/openapi.json'
    openapi_prefix: str = ''
    docs_url: Optional[str] = None
    redoc_url: str = '/redoc'
    static_url: str = None
    # Swagger UI配置
    swagger_ui_oauth2_redirect_url: str = '/docs/oauth2-redirect'
    swagger_js_url: str = "/static/docs-ui/swagger-ui-bundle.js"
    swagger_css_url: str = "/static/docs-ui/swagger-ui.css"
    swagger_ui_init_oauth: Optional[dict] = None

    # 其他配置
    res_path: str = '../res'
    request_log_to_mysql: bool = False
    lifespan: Optional[Lifespan] = None
    #lifespan = None

class DBConfig(BaseModel):
    """
    DbConfig DB配置类
    :version: 1.4
    :date: 2025.07.16
    """
    type: str = 'mysql'
    driver: str = 'mysql+pymysql'
    path: str = 'data.db'
    host: str = 'localhost'
    port: int = 3306
    username: str = 'root'
    password: str | None = Field(default=None)
    database: str = 'ai'
    charset: str = 'utf8mb4'
    echo: bool = True
    pool_size: int = 100
    max_overflow: int = 100
    # pool_timeout: int = 100
    pool_recycle: int = 60
    check_same_thread: bool = False
    def get_url(self):
        url = ''
        # sqlite_url = "sqlite:///mydatabase.db"
        # # 绝对路径（Windows 示例）
        # sqlite_url = "sqlite:///C:/projects/mydatabase.db"
        # # 绝对路径（Linux/macOS 示例）
        #sqlite_url = "sqlite:////home/<USER>/projects/mydatabase.db"
        # # 内存数据库（临时数据库，进程结束后消失）
        # sqlite_url = "sqlite:///:memory:"
        # # 带 SQLCipher 加密（需 pysqlcipher3 驱动）
        # sqlite_encrypted_url = "sqlite+pysqlcipher3://:{password}@/mydatabase.db"
        match self.type:
            case 'mysql':
                url = f'{self.driver}://{self.username}:{self.password}@{self.host}:{self.port}/{self.database}?charset={self.charset}'
            case 'sqlite':
                if self.password:
                    url = f'{self.driver}://:{self.password}@{self.path}?check_same_thread={self.check_same_thread}'
                else:
                    url = f'{self.driver}://{self.path}?check_same_thread={self.check_same_thread}'
            case _:
                url = ''
        return url

class ApikeyModel(BaseModel):
    tongyi: str = None
    openai: str = None
    claude: str = None
    gemini: str = None
    baidu: str = None

class ServiceStatus:
    Starting:str='Starting'
    Running:str = 'Running'
    WaitStop:str = 'WaitStop'
    Stoped:str = 'Stoped'

class ServiceConfig(BaseModel):
    status: str = ServiceStatus.Starting
    delay: int = 5


class EmailConfig(BaseModel):
    email: str=None
    auth_code: str=None

class ConfigModel(BaseModel):
    base: ConfigBase = None
    db: DBConfig = None
    redis: RedisConfig =None
    fastapi: FastapiConfig = None
    apikey: ApikeyModel = None
    service:ServiceConfig = ServiceConfig()
    email: EmailConfig = None

def get_config()->ConfigModel:
    try:
        config_filename = 'config.yml'
        if not os.path.exists(config_filename): config_filename = 'config.yaml'
        config_base = yaml_to_object(config_filename, ConfigBase)

        config_filename = f'config_{config_base.env}.yml'
        if not os.path.exists(config_filename): config_filename = f'config_{config_base.env}.yaml'
        config = yaml_to_object(config_filename, ConfigModel)
        config.base = config_base
        return config

    except Exception as e:
        logger.warning(f'配置文件解析失败: {e}')
        logger.warning("请在配置文件加载完成后，重新初始化配置。。")
        return ConfigModel()

Config = get_config()
