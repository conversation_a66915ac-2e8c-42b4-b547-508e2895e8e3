import os
import sys


def get_app_root_path():
    """
    获取程序根路径（兼容开发环境和打包后的环境）
    - 开发环境：返回项目根目录（main.py所在目录）
    - 打包后环境：返回TradingViewEmail.exe所在目录
    """
    if getattr(sys, 'frozen', False):
        # 打包后：sys.frozen为True，_MEIPASS是pyinstaller生成的临时目录（多文件模式下是exe所在目录）
        return os.path.dirname(sys.executable)
    else:
        # 开发环境：返回main.py所在的目录
        return os.path.dirname(os.path.abspath(__file__))