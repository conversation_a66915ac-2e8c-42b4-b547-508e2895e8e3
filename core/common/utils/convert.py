import json
from json import JSONEncoder
from sqlalchemy.orm import registry  # 导入 registry 类型

from datetime import datetime


class JSONEncoders(object):
    """
    定义JSONEncoders
    """
    json_encoders = {
        datetime: lambda dt: dt.isoformat(' ')  # 解决日期和时间中“T”字符的格式问题
    }

class CustomJSONEncoder(JSONEncoder):
    """
    自定义JSON编码处理
    :version: 1.1
    :date: 2019-01-08
    """
    def default(self, obj):
        # 处理 registry 类型
        if isinstance(obj, registry):
            return {
                "type": "SQLAlchemy registry",
                "metadata": str(obj.metadata)  # 只保留必要信息
            }
        # 处理其他常见类型（如 datetime）
        if isinstance(obj, datetime):
            return obj.isoformat()
        # 调用默认处理
        return super().default(obj)

def obj2dict(obj):
    if not obj:
        return None

    # 判断是否是Query
    # 定义一个字典对象
    dictionary = {}
    # 检索记录中的成员
    for field in [x for x in dir(obj) if
                  # 过滤属性
                  not x.startswith('_')
                  # 过滤掉方法属性
                  and hasattr(obj.__getattribute__(x), '__call__') == False
                  # 过滤掉不需要的属性
                  and x != 'metadata'
                  and x != 'query']:
        data = obj.__getattribute__(field)

        if hasattr(data, 'query'):
            data = obj2dict(data)

        try:
            dictionary[field] = data
        except TypeError:
            dictionary[field] = None

    # 返回字典对象
    return dictionary

def obj2json(obj) -> str:
    x_dict = obj2dict(obj)
    # x_json = json.dumps(x_dict, ensure_ascii=False, cls=CustomJSONEncoder)

    # 返回json字符串
    return dict2json(x_dict)

def dict2json(x_dict)->str:
    return json.dumps(x_dict, ensure_ascii=False, cls=CustomJSONEncoder)



def list_list2dict(x_list: list, key_index: int = 0, value_index: int = 1):
    x_dict = {}

    for x_item in x_list:
        if isinstance(x_item, list) or isinstance(x_item, tuple):
            if x_item[key_index]:
                x_dict[x_item[key_index]] = x_item[value_index]

    return x_dict


def list_dict2dict(x_list: list, key_key: str, value_key: str):
    x_dict = {}

    for x_item in x_list:
        if isinstance(x_item, dict):
            if key_key in x_item and value_key in x_item:
                x_dict[x_item[key_key]] = x_item[value_key]

    return x_dict


def json2obj(json_str,obj):
    x_dict = json.loads(json_str)
    return obj(**x_dict)