from typing import Any

import yaml
from pydantic import BaseModel

def lowercase_keys(obj: Any) -> Any:
    """递归将字典的所有键转为小写"""
    if isinstance(obj, dict):
        # 对字典的每个键转为小写，值递归处理
        return {k.lower(): lowercase_keys(v) for k, v in obj.items()}
    elif isinstance(obj, list):
        # 对列表中的每个元素递归处理
        return [lowercase_keys(item) for item in obj]
    else:
        # 非字典/列表类型直接返回
        return obj

def yaml_to_object(yaml_path: str, model_class: BaseModel):
    """
    将 YAML 文件转换为指定的 pydantic 模型对象
    :type model_class: BaseModel
    :param yaml_path: YAML 文件路径
    :param model_class: 目标模型类
    :return: 模型实例
    """
    # 读取 YAML 文件内容为字典
    with open(yaml_path, "r", encoding="utf-8") as f:
        yaml_data = yaml.safe_load(f)  # 安全解析 YAML 为字典
    yaml_data = lowercase_keys(yaml_data)
    # 将字典转换为模型对象（自动校验类型）
    return model_class(**yaml_data)
