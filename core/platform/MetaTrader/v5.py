from datetime import datetime, timedelta

import MetaTrader5 as mt5

from core.common.utils.logHelper import logger


# 连接到MT5终端
def connect_mt5():
    """连接到MT5终端"""
    if not mt5.initialize():
        logger.error(f"MT5初始化失败，错误代码: {mt5.last_error()}")
        return False
    logger.info("MT5连接成功")
    return True

def close_mt5():
    mt5.shutdown()
    logger.info("MT5连接已断开")

# 开仓函数
def open_order(symbol, volume, order_type, stop_loss=0, take_profit=0, deviation=10, comment='自动开仓'):
    """
    发送开仓订单
    :param comment:
    :param magic: 交易表示
    :param symbol: 交易品种（如"EURUSD"）
    :param volume: 交易量（手）
    :param order_type: 订单类型（mt5.ORDER_TYPE_BUY或mt5.ORDER_TYPE_SELL）
    :param stop_loss: 止损点数（可选）
    :param take_profit: 止盈点数（可选）
    :param deviation: 价格偏差（点）
    :return: 订单结果
    """
    # 获取交易品种信息
    symbol_info = mt5.symbol_info(symbol)
    if not symbol_info:
        logger.error(f"交易品种 {symbol} 不存在")
        return None

    # 确保品种可交易
    if not symbol_info.visible:
        if not mt5.symbol_select(symbol, True):
            logger.error(f"无法选择交易品种 {symbol}")
            return None

    # 获取当前价格
    tick = mt5.symbol_info_tick(symbol)
    if not tick:
        logger.error(f"无法获取 {symbol} 的报价")
        return None

    # 计算止损止盈价格
    point = symbol_info.point
    sl = 0.0
    tp = 0.0

    if stop_loss > 0:
        sl = tick.ask - stop_loss * point if order_type == mt5.ORDER_TYPE_BUY else tick.bid + stop_loss * point
    if take_profit > 0:
        tp = tick.ask + take_profit * point if order_type == mt5.ORDER_TYPE_BUY else tick.bid - take_profit * point

    # 准备订单请求
    request = {
        "action": mt5.TRADE_ACTION_DEAL,
        "symbol": symbol,
        "volume": volume,
        "type": order_type,
        "price": tick.ask if order_type == mt5.ORDER_TYPE_BUY else tick.bid,
        "sl": sl,
        "tp": tp,
        "deviation": deviation,
        "magic": 0,  # 魔术数，用于识别订单
        "comment": comment,
        "type_time": mt5.ORDER_TIME_GTC,  # 订单有效期：取消前有效
        "type_filling": mt5.ORDER_FILLING_FOK,  # 订单执行类型

    }

    # 发送订单
    result = mt5.order_send(request)
    if result is None:
        logger.error(f"订单发送失败，错误代码: {mt5.last_error()}")
        return None
    if result.retcode != mt5.TRADE_RETCODE_DONE:
        logger.error(f"订单发送失败，错误代码: {result.retcode}, 错误信息: {mt5.last_error()}")
        return None

    logger.info(f"订单成功执行，订单号: {result.order}")
    return result

# 平仓函数
def close_position(position):
    """
    平掉指定持仓
    :param position: 持仓对象（从positions_get获取）
    :return: 平仓结果
    """
    symbol = position.symbol
    tick = mt5.symbol_info_tick(symbol)
    if not tick:
        logger.error(f"无法获取 {symbol} 的报价")
        return None

    # 准备平仓订单请求
    request = {
        "action": mt5.TRADE_ACTION_DEAL,
        "symbol": symbol,
        "volume": position.volume,
        "type": mt5.ORDER_TYPE_SELL if position.type == mt5.POSITION_TYPE_BUY else mt5.ORDER_TYPE_BUY,
        "position": position.ticket,  # 指定要平仓的订单号
        "price": tick.bid if position.type == mt5.POSITION_TYPE_BUY else tick.ask,
        "deviation": 10,
        "magic": 12345,
        "comment": position.comment,
        "type_time": mt5.ORDER_TIME_GTC,
        "type_filling": mt5.ORDER_FILLING_FOK,
    }

    # 发送平仓订单
    result = mt5.order_send(request)
    if result.retcode != mt5.TRADE_RETCODE_DONE:
        logger.error(f"平仓失败，错误代码: {result.retcode}, 错误信息: {mt5.last_error()}")
        return None

    logger.info(f"平仓成功，订单号: {position.ticket}")
    return result

# 查询当前持仓
def get_open_positions(symbol=None)->list[mt5.TradePosition]:
    """
    查询当前持仓
    :param symbol: 可选，指定交易品种
    :return: 持仓列表
    """
    if symbol:
        positions = mt5.positions_get(symbol=symbol)
    else:
        positions = mt5.positions_get()

    if not positions:
        logger.info("没有当前持仓" if not symbol else f"没有 {symbol} 的持仓")
        return []
    # logger.info(f"当前持仓数量: {len(positions)}")
    # for pos in positions:
    #     logger.info(f"订单号: {pos.ticket}, 品种: {pos.symbol}, 类型: {'买入' if pos.type == mt5.POSITION_TYPE_BUY else '卖出'}, 手数: {pos.volume}, 开仓价: {pos.price_open}, 收益：{pos.profit}")
    return positions


def get_history_orders(symbol=None, magic_number=None, start_date=None, end_date=None):
    """
    查询MT5历史订单

    参数:
        symbol: 交易品种（如"EURUSD"，None则查询所有品种）
        magic_number: 魔术数字（None则查询所有魔术数字）
        start_date: 开始日期（datetime对象，None则从最早开始）
        end_date: 结束日期（datetime对象，None则到当前时间）

    返回:
        历史订单的DataFrame或None
    """
    # 设置默认时间范围（最近30天）
    if end_date is None:
        end_date = datetime.now()
    if start_date is None:
        start_date = end_date - timedelta(days=30)

    # 转换为MT5时间格式（秒级时间戳）
    start_timestamp = int(start_date.timestamp())
    end_timestamp = int(end_date.timestamp())

    # 查询历史订单
    # 注意：history_orders_get()的参数顺序是(start, end, symbol, magic)
    logger.debug(start_timestamp)
    logger.debug(end_timestamp)
    orders = mt5.history_orders_get(
        start_timestamp,
        end_timestamp,
        symbol=symbol,
        magic=magic_number
    )

    if orders is None:
        print(f"未查询到历史订单，错误码: {mt5.last_error()}")
        return None

    if len(orders) == 0:
        print("查询到0条历史订单")
        return None
    return orders




# 主函数示例
if __name__ == "__main__":
    # 连接MT5
    if not connect_mt5():
        exit()

    try:
        # 示例1：开一个EURUSD买入订单
        # order_o = open_order("XAUUSD", 1.00, mt5.ORDER_TYPE_BUY, comment='测试订单')
        # logger.debug(order_o)
        # # 示例2：查询所有持仓
        _positions = get_open_positions()
        # # ticket=53055594363, time=1756211805, time_msc=1756211805756, time_update=1756211805, time_update_msc=1756211805756, type=0, magic=0, identifier=53055594363, reason=3, volume=1.0, price_open=3372.93, sl=0.0, tp=0.0, price_current=3380.81, swap=-12.6, profit=788.0, symbol='XAUUSD', comment='', external_id=''
        # # for p in _positions:
        # #     print(p)
        # # 示例3：平掉第一个持仓（如果有）

        for p in _positions:
            logger.debug(p)
            logger.debug(p._asdict())

        _orders = get_history_orders()
        logger.debug(_orders[-1])
        logger.debug(_orders[-1]._asdict())


    finally:
        # 断开MT5连接
        mt5.shutdown()
        logger.info("MT5连接已断开")
