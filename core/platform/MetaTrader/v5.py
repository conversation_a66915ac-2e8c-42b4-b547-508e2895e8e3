import MetaTrader5 as mt5
import time

from core.common.utils.logHelper import logger


# 连接到MT5终端
def connect_mt5():
    """连接到MT5终端"""
    if not mt5.initialize():
        logger.error(f"MT5初始化失败，错误代码: {mt5.last_error()}")
        return False
    logger.info("MT5连接成功")
    return True

def close_mt5():
    mt5.shutdown()
    logger.info("MT5连接已断开")

# 开仓函数
def open_order(symbol, volume, order_type, stop_loss=0, take_profit=0, deviation=10, comment='自动开仓'):
    """
    发送开仓订单
    :param comment:
    :param magic: 交易表示
    :param symbol: 交易品种（如"EURUSD"）
    :param volume: 交易量（手）
    :param order_type: 订单类型（mt5.ORDER_TYPE_BUY或mt5.ORDER_TYPE_SELL）
    :param stop_loss: 止损点数（可选）
    :param take_profit: 止盈点数（可选）
    :param deviation: 价格偏差（点）
    :return: 订单结果
    """
    # 获取交易品种信息
    symbol_info = mt5.symbol_info(symbol)
    if not symbol_info:
        logger.error(f"交易品种 {symbol} 不存在")
        return None

    # 确保品种可交易
    if not symbol_info.visible:
        if not mt5.symbol_select(symbol, True):
            logger.error(f"无法选择交易品种 {symbol}")
            return None

    # 获取当前价格
    tick = mt5.symbol_info_tick(symbol)
    if not tick:
        logger.error(f"无法获取 {symbol} 的报价")
        return None

    # 计算止损止盈价格
    point = symbol_info.point
    sl = 0.0
    tp = 0.0

    if stop_loss > 0:
        sl = tick.ask - stop_loss * point if order_type == mt5.ORDER_TYPE_BUY else tick.bid + stop_loss * point
    if take_profit > 0:
        tp = tick.ask + take_profit * point if order_type == mt5.ORDER_TYPE_BUY else tick.bid - take_profit * point

    # 准备订单请求
    request = {
        "action": mt5.TRADE_ACTION_DEAL,
        "symbol": symbol,
        "volume": volume,
        "type": order_type,
        "price": tick.ask if order_type == mt5.ORDER_TYPE_BUY else tick.bid,
        "sl": sl,
        "tp": tp,
        "deviation": deviation,
        "magic": 0,  # 魔术数，用于识别订单
        "comment": comment,
        "type_time": mt5.ORDER_TIME_GTC,  # 订单有效期：取消前有效
        "type_filling": mt5.ORDER_FILLING_FOK,  # 订单执行类型

    }

    # 发送订单
    result = mt5.order_send(request)
    if result is None:
        logger.error(f"订单发送失败，错误代码: {mt5.last_error()}")
        return None
    if result.retcode != mt5.TRADE_RETCODE_DONE:
        logger.error(f"订单发送失败，错误代码: {result.retcode}, 错误信息: {mt5.last_error()}")
        return None

    logger.info(f"订单成功执行，订单号: {result.order}")
    return result

# 平仓函数
def close_position(position):
    """
    平掉指定持仓
    :param position: 持仓对象（从positions_get获取）
    :return: 平仓结果
    """
    symbol = position.symbol
    tick = mt5.symbol_info_tick(symbol)
    if not tick:
        logger.error(f"无法获取 {symbol} 的报价")
        return None

    # 准备平仓订单请求
    request = {
        "action": mt5.TRADE_ACTION_DEAL,
        "symbol": symbol,
        "volume": position.volume,
        "type": mt5.ORDER_TYPE_SELL if position.type == mt5.POSITION_TYPE_BUY else mt5.ORDER_TYPE_BUY,
        "position": position.ticket,  # 指定要平仓的订单号
        "price": tick.bid if position.type == mt5.POSITION_TYPE_BUY else tick.ask,
        "deviation": 10,
        "magic": 12345,
        "comment": "Python平仓订单",
        "type_time": mt5.ORDER_TIME_GTC,
        "type_filling": mt5.ORDER_FILLING_FOK,
    }

    # 发送平仓订单
    result = mt5.order_send(request)
    if result.retcode != mt5.TRADE_RETCODE_DONE:
        logger.error(f"平仓失败，错误代码: {result.retcode}, 错误信息: {mt5.last_error()}")
        return None

    logger.info(f"平仓成功，订单号: {position.ticket}")
    return result

# 查询当前持仓
def get_open_positions(symbol=None):
    """
    查询当前持仓
    :param symbol: 可选，指定交易品种
    :return: 持仓列表
    """
    if symbol:
        positions = mt5.positions_get(symbol=symbol)
    else:
        positions = mt5.positions_get()

    if not positions:
        logger.info("没有当前持仓" if not symbol else f"没有 {symbol} 的持仓")
        return []

    logger.info(f"当前持仓数量: {len(positions)}")
    for pos in positions:
        logger.info(f"订单号: {pos.ticket}, 品种: {pos.symbol}, 类型: {'买入' if pos.type == mt5.POSITION_TYPE_BUY else '卖出'}, 手数: {pos.volume}, 开仓价: {pos.price_open}")

    return positions



# 主函数示例
if __name__ == "__main__":
    # 连接MT5
    if not connect_mt5():
        exit()

    try:
        # 示例1：开一个EURUSD买入订单
        open_order("EURUSD", 0.01, mt5.ORDER_TYPE_BUY, stop_loss=50, take_profit=100,comment='123')

        # 示例2：查询所有持仓
        _positions = get_open_positions()
        for p in _positions:
            print(p)
        # 示例3：平掉第一个持仓（如果有）
        # if _positions:
        #     close_position(_positions[0])

    finally:
        # 断开MT5连接
        mt5.shutdown()
        logger.info("MT5连接已断开")
