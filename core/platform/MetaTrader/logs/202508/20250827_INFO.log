[2025-08-27 12:00:38.315] v5.py->connect_mt5 line:13 [INFO] : MT5连接成功
[2025-08-27 12:00:38.315] v5.py->get_open_positions line:144 [INFO] : 当前持仓数量: 16
[2025-08-27 12:00:38.315] v5.py->get_open_positions line:146 [INFO] : 订单号: 53055594363, 品种: XAUUSD, 类型: 买入, 手数: 1.0, 开仓价: 3372.93
[2025-08-27 12:00:38.315] v5.py->get_open_positions line:146 [INFO] : 订单号: 53055594500, 品种: XAUUSD, 类型: 买入, 手数: 1.0, 开仓价: 3372.92
[2025-08-27 12:00:38.315] v5.py->get_open_positions line:146 [INFO] : 订单号: 53055594614, 品种: XAUUSD, 类型: 买入, 手数: 1.0, 开仓价: 3372.95
[2025-08-27 12:00:38.315] v5.py->get_open_positions line:146 [INFO] : 订单号: 53055594706, 品种: XAUUSD, 类型: 买入, 手数: 1.0, 开仓价: 3372.84
[2025-08-27 12:00:38.316] v5.py->get_open_positions line:146 [INFO] : 订单号: 53055594816, 品种: XAUUSD, 类型: 买入, 手数: 1.0, 开仓价: 3372.84
[2025-08-27 12:00:38.316] v5.py->get_open_positions line:146 [INFO] : 订单号: 53055595051, 品种: XAUUSD, 类型: 买入, 手数: 1.0, 开仓价: 3372.89
[2025-08-27 12:00:38.316] v5.py->get_open_positions line:146 [INFO] : 订单号: 53055595084, 品种: XAUUSD, 类型: 买入, 手数: 1.0, 开仓价: 3372.89
[2025-08-27 12:00:38.316] v5.py->get_open_positions line:146 [INFO] : 订单号: 53055595125, 品种: XAUUSD, 类型: 买入, 手数: 1.0, 开仓价: 3372.89
[2025-08-27 12:00:38.316] v5.py->get_open_positions line:146 [INFO] : 订单号: 53055595215, 品种: XAUUSD, 类型: 买入, 手数: 1.0, 开仓价: 3372.89
[2025-08-27 12:00:38.316] v5.py->get_open_positions line:146 [INFO] : 订单号: 53055595247, 品种: XAUUSD, 类型: 买入, 手数: 1.0, 开仓价: 3372.9
[2025-08-27 12:00:38.316] v5.py->get_open_positions line:146 [INFO] : 订单号: 53055595259, 品种: XAUUSD, 类型: 买入, 手数: 1.0, 开仓价: 3372.9
[2025-08-27 12:00:38.316] v5.py->get_open_positions line:146 [INFO] : 订单号: 53055595282, 品种: XAUUSD, 类型: 买入, 手数: 1.0, 开仓价: 3372.9
[2025-08-27 12:00:38.316] v5.py->get_open_positions line:146 [INFO] : 订单号: 53055595310, 品种: XAUUSD, 类型: 买入, 手数: 1.0, 开仓价: 3372.89
[2025-08-27 12:00:38.316] v5.py->get_open_positions line:146 [INFO] : 订单号: 53055595360, 品种: XAUUSD, 类型: 买入, 手数: 1.0, 开仓价: 3372.89
[2025-08-27 12:00:38.316] v5.py->get_open_positions line:146 [INFO] : 订单号: 53055595405, 品种: XAUUSD, 类型: 买入, 手数: 1.0, 开仓价: 3372.89
[2025-08-27 12:00:38.317] v5.py->get_open_positions line:146 [INFO] : 订单号: 53055595457, 品种: XAUUSD, 类型: 买入, 手数: 1.0, 开仓价: 3372.9
[2025-08-27 12:00:38.319] v5.py-><module> line:173 [INFO] : MT5连接已断开
[2025-08-27 12:04:23.965] v5.py->connect_mt5 line:15 [INFO] : MT5连接成功
[2025-08-27 12:04:23.965] v5.py->get_open_positions line:146 [INFO] : 当前持仓数量: 16
[2025-08-27 12:04:23.966] v5.py->get_open_positions line:148 [INFO] : 订单号: 53055594363, 品种: XAUUSD, 类型: 买入, 手数: 1.0, 开仓价: 3372.93
[2025-08-27 12:04:23.966] v5.py->get_open_positions line:148 [INFO] : 订单号: 53055594500, 品种: XAUUSD, 类型: 买入, 手数: 1.0, 开仓价: 3372.92
[2025-08-27 12:04:23.966] v5.py->get_open_positions line:148 [INFO] : 订单号: 53055594614, 品种: XAUUSD, 类型: 买入, 手数: 1.0, 开仓价: 3372.95
[2025-08-27 12:04:23.966] v5.py->get_open_positions line:148 [INFO] : 订单号: 53055594706, 品种: XAUUSD, 类型: 买入, 手数: 1.0, 开仓价: 3372.84
[2025-08-27 12:04:23.966] v5.py->get_open_positions line:148 [INFO] : 订单号: 53055594816, 品种: XAUUSD, 类型: 买入, 手数: 1.0, 开仓价: 3372.84
[2025-08-27 12:04:23.966] v5.py->get_open_positions line:148 [INFO] : 订单号: 53055595051, 品种: XAUUSD, 类型: 买入, 手数: 1.0, 开仓价: 3372.89
[2025-08-27 12:04:23.966] v5.py->get_open_positions line:148 [INFO] : 订单号: 53055595084, 品种: XAUUSD, 类型: 买入, 手数: 1.0, 开仓价: 3372.89
[2025-08-27 12:04:23.966] v5.py->get_open_positions line:148 [INFO] : 订单号: 53055595125, 品种: XAUUSD, 类型: 买入, 手数: 1.0, 开仓价: 3372.89
[2025-08-27 12:04:23.966] v5.py->get_open_positions line:148 [INFO] : 订单号: 53055595215, 品种: XAUUSD, 类型: 买入, 手数: 1.0, 开仓价: 3372.89
[2025-08-27 12:04:23.966] v5.py->get_open_positions line:148 [INFO] : 订单号: 53055595247, 品种: XAUUSD, 类型: 买入, 手数: 1.0, 开仓价: 3372.9
[2025-08-27 12:04:23.966] v5.py->get_open_positions line:148 [INFO] : 订单号: 53055595259, 品种: XAUUSD, 类型: 买入, 手数: 1.0, 开仓价: 3372.9
[2025-08-27 12:04:23.967] v5.py->get_open_positions line:148 [INFO] : 订单号: 53055595282, 品种: XAUUSD, 类型: 买入, 手数: 1.0, 开仓价: 3372.9
[2025-08-27 12:04:23.967] v5.py->get_open_positions line:148 [INFO] : 订单号: 53055595310, 品种: XAUUSD, 类型: 买入, 手数: 1.0, 开仓价: 3372.89
[2025-08-27 12:04:23.967] v5.py->get_open_positions line:148 [INFO] : 订单号: 53055595360, 品种: XAUUSD, 类型: 买入, 手数: 1.0, 开仓价: 3372.89
[2025-08-27 12:04:23.967] v5.py->get_open_positions line:148 [INFO] : 订单号: 53055595405, 品种: XAUUSD, 类型: 买入, 手数: 1.0, 开仓价: 3372.89
[2025-08-27 12:04:23.967] v5.py->get_open_positions line:148 [INFO] : 订单号: 53055595457, 品种: XAUUSD, 类型: 买入, 手数: 1.0, 开仓价: 3372.9
[2025-08-27 12:04:24.497] v5.py->close_position line:127 [INFO] : 平仓成功，订单号: 53055594363
[2025-08-27 12:04:24.498] v5.py-><module> line:177 [INFO] : MT5连接已断开
[2025-08-27 12:06:19.547] v5.py->connect_mt5 line:16 [INFO] : MT5连接成功
[2025-08-27 12:06:19.548] v5.py->get_open_positions line:147 [INFO] : 当前持仓数量: 15
[2025-08-27 12:06:19.548] v5.py->get_open_positions line:149 [INFO] : 订单号: 53055594500, 品种: XAUUSD, 类型: 买入, 手数: 1.0, 开仓价: 3372.92
[2025-08-27 12:06:19.548] v5.py->get_open_positions line:149 [INFO] : 订单号: 53055594614, 品种: XAUUSD, 类型: 买入, 手数: 1.0, 开仓价: 3372.95
[2025-08-27 12:06:19.548] v5.py->get_open_positions line:149 [INFO] : 订单号: 53055594706, 品种: XAUUSD, 类型: 买入, 手数: 1.0, 开仓价: 3372.84
[2025-08-27 12:06:19.548] v5.py->get_open_positions line:149 [INFO] : 订单号: 53055594816, 品种: XAUUSD, 类型: 买入, 手数: 1.0, 开仓价: 3372.84
[2025-08-27 12:06:19.548] v5.py->get_open_positions line:149 [INFO] : 订单号: 53055595051, 品种: XAUUSD, 类型: 买入, 手数: 1.0, 开仓价: 3372.89
[2025-08-27 12:06:19.548] v5.py->get_open_positions line:149 [INFO] : 订单号: 53055595084, 品种: XAUUSD, 类型: 买入, 手数: 1.0, 开仓价: 3372.89
[2025-08-27 12:06:19.548] v5.py->get_open_positions line:149 [INFO] : 订单号: 53055595125, 品种: XAUUSD, 类型: 买入, 手数: 1.0, 开仓价: 3372.89
[2025-08-27 12:06:19.548] v5.py->get_open_positions line:149 [INFO] : 订单号: 53055595215, 品种: XAUUSD, 类型: 买入, 手数: 1.0, 开仓价: 3372.89
[2025-08-27 12:06:19.549] v5.py->get_open_positions line:149 [INFO] : 订单号: 53055595247, 品种: XAUUSD, 类型: 买入, 手数: 1.0, 开仓价: 3372.9
[2025-08-27 12:06:19.549] v5.py->get_open_positions line:149 [INFO] : 订单号: 53055595259, 品种: XAUUSD, 类型: 买入, 手数: 1.0, 开仓价: 3372.9
[2025-08-27 12:06:19.549] v5.py->get_open_positions line:149 [INFO] : 订单号: 53055595282, 品种: XAUUSD, 类型: 买入, 手数: 1.0, 开仓价: 3372.9
[2025-08-27 12:06:19.549] v5.py->get_open_positions line:149 [INFO] : 订单号: 53055595310, 品种: XAUUSD, 类型: 买入, 手数: 1.0, 开仓价: 3372.89
[2025-08-27 12:06:19.549] v5.py->get_open_positions line:149 [INFO] : 订单号: 53055595360, 品种: XAUUSD, 类型: 买入, 手数: 1.0, 开仓价: 3372.89
[2025-08-27 12:06:19.549] v5.py->get_open_positions line:149 [INFO] : 订单号: 53055595405, 品种: XAUUSD, 类型: 买入, 手数: 1.0, 开仓价: 3372.89
[2025-08-27 12:06:19.549] v5.py->get_open_positions line:149 [INFO] : 订单号: 53055595457, 品种: XAUUSD, 类型: 买入, 手数: 1.0, 开仓价: 3372.9
[2025-08-27 12:06:20.644] v5.py->close_position line:128 [INFO] : 平仓成功，订单号: 53055594500
[2025-08-27 12:06:20.645] v5.py-><module> line:178 [INFO] : MT5连接已断开
[2025-08-27 12:08:23.036] v5.py->connect_mt5 line:16 [INFO] : MT5连接成功
[2025-08-27 12:08:23.036] v5.py->get_open_positions line:147 [INFO] : 当前持仓数量: 14
[2025-08-27 12:08:23.036] v5.py->get_open_positions line:149 [INFO] : 订单号: 53055594614, 品种: XAUUSD, 类型: 买入, 手数: 1.0, 开仓价: 3372.95
[2025-08-27 12:08:23.036] v5.py->get_open_positions line:149 [INFO] : 订单号: 53055594706, 品种: XAUUSD, 类型: 买入, 手数: 1.0, 开仓价: 3372.84
[2025-08-27 12:08:23.036] v5.py->get_open_positions line:149 [INFO] : 订单号: 53055594816, 品种: XAUUSD, 类型: 买入, 手数: 1.0, 开仓价: 3372.84
[2025-08-27 12:08:23.036] v5.py->get_open_positions line:149 [INFO] : 订单号: 53055595051, 品种: XAUUSD, 类型: 买入, 手数: 1.0, 开仓价: 3372.89
[2025-08-27 12:08:23.036] v5.py->get_open_positions line:149 [INFO] : 订单号: 53055595084, 品种: XAUUSD, 类型: 买入, 手数: 1.0, 开仓价: 3372.89
[2025-08-27 12:08:23.036] v5.py->get_open_positions line:149 [INFO] : 订单号: 53055595125, 品种: XAUUSD, 类型: 买入, 手数: 1.0, 开仓价: 3372.89
[2025-08-27 12:08:23.036] v5.py->get_open_positions line:149 [INFO] : 订单号: 53055595215, 品种: XAUUSD, 类型: 买入, 手数: 1.0, 开仓价: 3372.89
[2025-08-27 12:08:23.037] v5.py->get_open_positions line:149 [INFO] : 订单号: 53055595247, 品种: XAUUSD, 类型: 买入, 手数: 1.0, 开仓价: 3372.9
[2025-08-27 12:08:23.037] v5.py->get_open_positions line:149 [INFO] : 订单号: 53055595259, 品种: XAUUSD, 类型: 买入, 手数: 1.0, 开仓价: 3372.9
[2025-08-27 12:08:23.037] v5.py->get_open_positions line:149 [INFO] : 订单号: 53055595282, 品种: XAUUSD, 类型: 买入, 手数: 1.0, 开仓价: 3372.9
[2025-08-27 12:08:23.037] v5.py->get_open_positions line:149 [INFO] : 订单号: 53055595310, 品种: XAUUSD, 类型: 买入, 手数: 1.0, 开仓价: 3372.89
[2025-08-27 12:08:23.037] v5.py->get_open_positions line:149 [INFO] : 订单号: 53055595360, 品种: XAUUSD, 类型: 买入, 手数: 1.0, 开仓价: 3372.89
[2025-08-27 12:08:23.037] v5.py->get_open_positions line:149 [INFO] : 订单号: 53055595405, 品种: XAUUSD, 类型: 买入, 手数: 1.0, 开仓价: 3372.89
[2025-08-27 12:08:23.037] v5.py->get_open_positions line:149 [INFO] : 订单号: 53055595457, 品种: XAUUSD, 类型: 买入, 手数: 1.0, 开仓价: 3372.9
[2025-08-27 12:08:23.682] v5.py->close_position line:128 [INFO] : 平仓成功，订单号: 53055594614
[2025-08-27 12:08:23.683] v5.py-><module> line:178 [INFO] : MT5连接已断开
[2025-08-27 12:10:04.670] v5.py->connect_mt5 line:16 [INFO] : MT5连接成功
[2025-08-27 12:10:04.670] v5.py->get_open_positions line:147 [INFO] : 当前持仓数量: 13
[2025-08-27 12:10:04.670] v5.py->get_open_positions line:149 [INFO] : 订单号: 53055594706, 品种: XAUUSD, 类型: 买入, 手数: 1.0, 开仓价: 3372.84
[2025-08-27 12:10:04.671] v5.py->get_open_positions line:149 [INFO] : 订单号: 53055594816, 品种: XAUUSD, 类型: 买入, 手数: 1.0, 开仓价: 3372.84
[2025-08-27 12:10:04.671] v5.py->get_open_positions line:149 [INFO] : 订单号: 53055595051, 品种: XAUUSD, 类型: 买入, 手数: 1.0, 开仓价: 3372.89
[2025-08-27 12:10:04.671] v5.py->get_open_positions line:149 [INFO] : 订单号: 53055595084, 品种: XAUUSD, 类型: 买入, 手数: 1.0, 开仓价: 3372.89
[2025-08-27 12:10:04.671] v5.py->get_open_positions line:149 [INFO] : 订单号: 53055595125, 品种: XAUUSD, 类型: 买入, 手数: 1.0, 开仓价: 3372.89
[2025-08-27 12:10:04.671] v5.py->get_open_positions line:149 [INFO] : 订单号: 53055595215, 品种: XAUUSD, 类型: 买入, 手数: 1.0, 开仓价: 3372.89
[2025-08-27 12:10:04.671] v5.py->get_open_positions line:149 [INFO] : 订单号: 53055595247, 品种: XAUUSD, 类型: 买入, 手数: 1.0, 开仓价: 3372.9
[2025-08-27 12:10:04.671] v5.py->get_open_positions line:149 [INFO] : 订单号: 53055595259, 品种: XAUUSD, 类型: 买入, 手数: 1.0, 开仓价: 3372.9
[2025-08-27 12:10:04.671] v5.py->get_open_positions line:149 [INFO] : 订单号: 53055595282, 品种: XAUUSD, 类型: 买入, 手数: 1.0, 开仓价: 3372.9
[2025-08-27 12:10:04.671] v5.py->get_open_positions line:149 [INFO] : 订单号: 53055595310, 品种: XAUUSD, 类型: 买入, 手数: 1.0, 开仓价: 3372.89
[2025-08-27 12:10:04.671] v5.py->get_open_positions line:149 [INFO] : 订单号: 53055595360, 品种: XAUUSD, 类型: 买入, 手数: 1.0, 开仓价: 3372.89
[2025-08-27 12:10:04.671] v5.py->get_open_positions line:149 [INFO] : 订单号: 53055595405, 品种: XAUUSD, 类型: 买入, 手数: 1.0, 开仓价: 3372.89
[2025-08-27 12:10:04.671] v5.py->get_open_positions line:149 [INFO] : 订单号: 53055595457, 品种: XAUUSD, 类型: 买入, 手数: 1.0, 开仓价: 3372.9
[2025-08-27 12:10:05.009] v5.py->close_position line:128 [INFO] : 平仓成功，订单号: 53055594706
[2025-08-27 12:10:05.010] v5.py-><module> line:179 [INFO] : MT5连接已断开
[2025-08-27 12:12:52.741] v5.py->connect_mt5 line:16 [INFO] : MT5连接成功
[2025-08-27 12:12:52.741] v5.py->get_open_positions line:147 [INFO] : 当前持仓数量: 12
[2025-08-27 12:12:52.741] v5.py->get_open_positions line:149 [INFO] : 订单号: 53055594816, 品种: XAUUSD, 类型: 买入, 手数: 1.0, 开仓价: 3372.84, 收益：642.0
[2025-08-27 12:12:52.741] v5.py->get_open_positions line:149 [INFO] : 订单号: 53055595051, 品种: XAUUSD, 类型: 买入, 手数: 1.0, 开仓价: 3372.89, 收益：637.0
[2025-08-27 12:12:52.741] v5.py->get_open_positions line:149 [INFO] : 订单号: 53055595084, 品种: XAUUSD, 类型: 买入, 手数: 1.0, 开仓价: 3372.89, 收益：637.0
[2025-08-27 12:12:52.741] v5.py->get_open_positions line:149 [INFO] : 订单号: 53055595125, 品种: XAUUSD, 类型: 买入, 手数: 1.0, 开仓价: 3372.89, 收益：637.0
[2025-08-27 12:12:52.741] v5.py->get_open_positions line:149 [INFO] : 订单号: 53055595215, 品种: XAUUSD, 类型: 买入, 手数: 1.0, 开仓价: 3372.89, 收益：637.0
[2025-08-27 12:12:52.741] v5.py->get_open_positions line:149 [INFO] : 订单号: 53055595247, 品种: XAUUSD, 类型: 买入, 手数: 1.0, 开仓价: 3372.9, 收益：636.0
[2025-08-27 12:12:52.742] v5.py->get_open_positions line:149 [INFO] : 订单号: 53055595259, 品种: XAUUSD, 类型: 买入, 手数: 1.0, 开仓价: 3372.9, 收益：636.0
[2025-08-27 12:12:52.742] v5.py->get_open_positions line:149 [INFO] : 订单号: 53055595282, 品种: XAUUSD, 类型: 买入, 手数: 1.0, 开仓价: 3372.9, 收益：636.0
[2025-08-27 12:12:52.742] v5.py->get_open_positions line:149 [INFO] : 订单号: 53055595310, 品种: XAUUSD, 类型: 买入, 手数: 1.0, 开仓价: 3372.89, 收益：637.0
[2025-08-27 12:12:52.742] v5.py->get_open_positions line:149 [INFO] : 订单号: 53055595360, 品种: XAUUSD, 类型: 买入, 手数: 1.0, 开仓价: 3372.89, 收益：637.0
[2025-08-27 12:12:52.742] v5.py->get_open_positions line:149 [INFO] : 订单号: 53055595405, 品种: XAUUSD, 类型: 买入, 手数: 1.0, 开仓价: 3372.89, 收益：637.0
[2025-08-27 12:12:52.742] v5.py->get_open_positions line:149 [INFO] : 订单号: 53055595457, 品种: XAUUSD, 类型: 买入, 手数: 1.0, 开仓价: 3372.9, 收益：636.0
[2025-08-27 12:12:53.072] v5.py->close_position line:128 [INFO] : 平仓成功，订单号: 53055594816
[2025-08-27 12:12:53.072] v5.py-><module> line:179 [INFO] : MT5连接已断开
[2025-08-27 12:14:23.046] v5.py->connect_mt5 line:16 [INFO] : MT5连接成功
[2025-08-27 12:14:23.377] v5.py->open_order line:91 [INFO] : 订单成功执行，订单号: 53061765894
[2025-08-27 12:14:23.377] v5.py-><module> line:175 [INFO] : MT5连接已断开
[2025-08-27 12:14:59.002] v5.py->connect_mt5 line:16 [INFO] : MT5连接成功
[2025-08-27 12:14:59.323] v5.py->open_order line:91 [INFO] : 订单成功执行，订单号: 53061769214
[2025-08-27 12:14:59.324] v5.py-><module> line:175 [INFO] : MT5连接已断开
[2025-08-27 12:28:27.854] v5.py->connect_mt5 line:16 [INFO] : MT5连接成功
[2025-08-27 12:28:28.181] v5.py->open_order line:91 [INFO] : 订单成功执行，订单号: 53061841559
[2025-08-27 12:28:28.510] v5.py->close_position line:128 [INFO] : 平仓成功，订单号: 53055595051
[2025-08-27 12:28:28.845] v5.py->close_position line:128 [INFO] : 平仓成功，订单号: 53055595084
[2025-08-27 12:28:29.178] v5.py->close_position line:128 [INFO] : 平仓成功，订单号: 53055595125
[2025-08-27 12:28:29.507] v5.py->close_position line:128 [INFO] : 平仓成功，订单号: 53055595215
[2025-08-27 12:28:30.407] v5.py->close_position line:128 [INFO] : 平仓成功，订单号: 53055595247
[2025-08-27 12:28:30.988] v5.py->close_position line:128 [INFO] : 平仓成功，订单号: 53055595259
[2025-08-27 12:28:31.316] v5.py->close_position line:128 [INFO] : 平仓成功，订单号: 53055595282
[2025-08-27 12:28:31.958] v5.py->close_position line:128 [INFO] : 平仓成功，订单号: 53055595310
[2025-08-27 12:28:32.603] v5.py->close_position line:128 [INFO] : 平仓成功，订单号: 53055595360
[2025-08-27 12:28:32.929] v5.py->close_position line:128 [INFO] : 平仓成功，订单号: 53055595405
[2025-08-27 12:28:33.760] v5.py->close_position line:128 [INFO] : 平仓成功，订单号: 53055595457
[2025-08-27 12:28:34.086] v5.py->close_position line:128 [INFO] : 平仓成功，订单号: 53061769214
[2025-08-27 12:28:34.408] v5.py->close_position line:128 [INFO] : 平仓成功，订单号: 53061841559
[2025-08-27 12:28:34.409] v5.py-><module> line:176 [INFO] : MT5连接已断开
[2025-08-27 12:30:06.951] v5.py->connect_mt5 line:16 [INFO] : MT5连接成功
[2025-08-27 12:30:06.952] v5.py->get_open_positions line:144 [INFO] : 没有当前持仓
[2025-08-27 12:30:06.952] v5.py-><module> line:176 [INFO] : MT5连接已断开
[2025-08-27 12:30:19.005] v5.py->connect_mt5 line:16 [INFO] : MT5连接成功
[2025-08-27 12:30:19.347] v5.py->open_order line:91 [INFO] : 订单成功执行，订单号: 53061853746
[2025-08-27 12:30:19.990] v5.py->close_position line:128 [INFO] : 平仓成功，订单号: 53061853746
[2025-08-27 12:30:19.991] v5.py-><module> line:176 [INFO] : MT5连接已断开
[2025-08-27 12:33:14.600] v5.py->connect_mt5 line:16 [INFO] : MT5连接成功
[2025-08-27 12:33:14.600] v5.py-><module> line:184 [INFO] : MT5连接已断开
[2025-08-27 12:36:59.856] v5.py->connect_mt5 line:16 [INFO] : MT5连接成功
[2025-08-27 12:36:59.857] v5.py-><module> line:277 [INFO] : MT5连接已断开
[2025-08-27 12:37:46.473] v5.py->connect_mt5 line:16 [INFO] : MT5连接成功
[2025-08-27 12:37:46.484] v5.py-><module> line:278 [INFO] : MT5连接已断开
[2025-08-27 12:38:32.664] v5.py->connect_mt5 line:16 [INFO] : MT5连接成功
[2025-08-27 12:38:32.665] v5.py-><module> line:278 [INFO] : MT5连接已断开
[2025-08-27 12:39:18.390] v5.py->connect_mt5 line:16 [INFO] : MT5连接成功
[2025-08-27 12:39:18.390] v5.py-><module> line:278 [INFO] : MT5连接已断开
[2025-08-27 12:52:16.630] v5.py->connect_mt5 line:16 [INFO] : MT5连接成功
[2025-08-27 12:52:16.652] v5.py-><module> line:268 [INFO] : MT5连接已断开
[2025-08-27 12:52:57.787] v5.py->connect_mt5 line:16 [INFO] : MT5连接成功
[2025-08-27 12:52:57.799] v5.py-><module> line:268 [INFO] : MT5连接已断开
[2025-08-27 12:53:42.586] v5.py->connect_mt5 line:16 [INFO] : MT5连接成功
[2025-08-27 12:53:42.596] v5.py-><module> line:267 [INFO] : MT5连接已断开
[2025-08-27 12:57:42.658] v5.py->connect_mt5 line:16 [INFO] : MT5连接成功
[2025-08-27 12:57:42.673] v5.py-><module> line:268 [INFO] : MT5连接已断开
[2025-08-27 13:01:45.925] v5.py->connect_mt5 line:16 [INFO] : MT5连接成功
[2025-08-27 13:01:45.933] v5.py-><module> line:270 [INFO] : MT5连接已断开
[2025-08-27 13:02:19.750] v5.py->connect_mt5 line:16 [INFO] : MT5连接成功
[2025-08-27 13:02:19.750] v5.py-><module> line:271 [INFO] : MT5连接已断开
[2025-08-27 13:02:43.573] v5.py->connect_mt5 line:16 [INFO] : MT5连接成功
[2025-08-27 13:02:43.582] v5.py-><module> line:249 [INFO] : MT5连接已断开
[2025-08-27 13:02:53.108] v5.py->connect_mt5 line:16 [INFO] : MT5连接成功
[2025-08-27 13:02:53.108] v5.py-><module> line:249 [INFO] : MT5连接已断开
[2025-08-27 13:03:05.505] v5.py->connect_mt5 line:16 [INFO] : MT5连接成功
[2025-08-27 13:03:05.515] v5.py-><module> line:249 [INFO] : MT5连接已断开
[2025-08-27 13:03:19.215] v5.py->connect_mt5 line:16 [INFO] : MT5连接成功
[2025-08-27 13:03:19.225] v5.py-><module> line:249 [INFO] : MT5连接已断开
[2025-08-27 13:03:30.456] v5.py->connect_mt5 line:16 [INFO] : MT5连接成功
[2025-08-27 13:03:30.456] v5.py-><module> line:249 [INFO] : MT5连接已断开
[2025-08-27 13:05:24.982] v5.py->connect_mt5 line:16 [INFO] : MT5连接成功
[2025-08-27 13:05:24.983] v5.py-><module> line:249 [INFO] : MT5连接已断开
[2025-08-27 13:05:34.119] v5.py->connect_mt5 line:16 [INFO] : MT5连接成功
[2025-08-27 13:05:34.441] v5.py->open_order line:91 [INFO] : 订单成功执行，订单号: 53062071935
[2025-08-27 13:05:34.442] v5.py-><module> line:249 [INFO] : MT5连接已断开
[2025-08-27 13:05:50.740] v5.py->connect_mt5 line:16 [INFO] : MT5连接成功
[2025-08-27 13:05:50.740] v5.py-><module> line:249 [INFO] : MT5连接已断开
[2025-08-27 13:06:06.120] v5.py->connect_mt5 line:16 [INFO] : MT5连接成功
[2025-08-27 13:06:06.120] v5.py-><module> line:249 [INFO] : MT5连接已断开
[2025-08-27 13:06:25.529] v5.py->connect_mt5 line:16 [INFO] : MT5连接成功
[2025-08-27 13:06:25.537] v5.py-><module> line:249 [INFO] : MT5连接已断开
[2025-08-27 13:08:13.316] v5.py->connect_mt5 line:16 [INFO] : MT5连接成功
[2025-08-27 13:08:13.324] v5.py-><module> line:251 [INFO] : MT5连接已断开
[2025-08-27 13:08:19.867] v5.py->connect_mt5 line:16 [INFO] : MT5连接成功
[2025-08-27 13:08:19.868] v5.py-><module> line:251 [INFO] : MT5连接已断开
[2025-08-27 13:09:50.529] v5.py->connect_mt5 line:16 [INFO] : MT5连接成功
[2025-08-27 13:09:50.541] v5.py-><module> line:251 [INFO] : MT5连接已断开
[2025-08-27 13:09:55.194] v5.py->connect_mt5 line:16 [INFO] : MT5连接成功
[2025-08-27 13:09:55.194] v5.py-><module> line:251 [INFO] : MT5连接已断开
[2025-08-27 13:10:02.082] v5.py->connect_mt5 line:16 [INFO] : MT5连接成功
[2025-08-27 13:10:02.083] v5.py-><module> line:251 [INFO] : MT5连接已断开
[2025-08-27 13:10:35.310] v5.py->connect_mt5 line:16 [INFO] : MT5连接成功
[2025-08-27 13:10:35.311] v5.py-><module> line:250 [INFO] : MT5连接已断开
[2025-08-27 13:11:18.470] v5.py->connect_mt5 line:16 [INFO] : MT5连接成功
[2025-08-27 13:11:18.470] v5.py-><module> line:251 [INFO] : MT5连接已断开
[2025-08-27 13:11:33.509] v5.py->connect_mt5 line:16 [INFO] : MT5连接成功
[2025-08-27 13:11:33.852] v5.py->open_order line:91 [INFO] : 订单成功执行，订单号: 53062107710
[2025-08-27 13:11:33.853] v5.py-><module> line:251 [INFO] : MT5连接已断开
[2025-08-27 13:11:42.432] v5.py->connect_mt5 line:16 [INFO] : MT5连接成功
[2025-08-27 13:11:42.432] v5.py-><module> line:251 [INFO] : MT5连接已断开
[2025-08-27 13:11:56.088] v5.py->connect_mt5 line:16 [INFO] : MT5连接成功
[2025-08-27 13:11:56.089] v5.py-><module> line:251 [INFO] : MT5连接已断开
[2025-08-27 13:12:23.961] v5.py->connect_mt5 line:16 [INFO] : MT5连接成功
[2025-08-27 13:12:23.961] v5.py-><module> line:250 [INFO] : MT5连接已断开
[2025-08-27 13:12:38.757] v5.py->connect_mt5 line:16 [INFO] : MT5连接成功
[2025-08-27 13:12:38.757] v5.py-><module> line:250 [INFO] : MT5连接已断开
[2025-08-27 13:13:05.354] v5.py->connect_mt5 line:16 [INFO] : MT5连接成功
[2025-08-27 13:13:05.355] v5.py-><module> line:252 [INFO] : MT5连接已断开
[2025-08-27 13:13:10.917] v5.py->connect_mt5 line:16 [INFO] : MT5连接成功
[2025-08-27 13:13:10.918] v5.py-><module> line:252 [INFO] : MT5连接已断开
[2025-08-27 13:13:12.938] v5.py->connect_mt5 line:16 [INFO] : MT5连接成功
[2025-08-27 13:13:12.939] v5.py-><module> line:252 [INFO] : MT5连接已断开
[2025-08-27 13:13:15.476] v5.py->connect_mt5 line:16 [INFO] : MT5连接成功
[2025-08-27 13:13:15.477] v5.py-><module> line:252 [INFO] : MT5连接已断开
[2025-08-27 13:13:17.508] v5.py->connect_mt5 line:16 [INFO] : MT5连接成功
[2025-08-27 13:13:17.508] v5.py-><module> line:252 [INFO] : MT5连接已断开
[2025-08-27 13:13:19.232] v5.py->connect_mt5 line:16 [INFO] : MT5连接成功
[2025-08-27 13:13:19.233] v5.py-><module> line:252 [INFO] : MT5连接已断开
[2025-08-27 13:17:03.474] v5.py->connect_mt5 line:16 [INFO] : MT5连接成功
[2025-08-27 13:17:03.474] v5.py-><module> line:252 [INFO] : MT5连接已断开
[2025-08-27 13:19:22.584] v5.py->connect_mt5 line:16 [INFO] : MT5连接成功
[2025-08-27 13:19:22.584] v5.py-><module> line:253 [INFO] : MT5连接已断开
[2025-08-27 13:20:24.294] v5.py->connect_mt5 line:16 [INFO] : MT5连接成功
[2025-08-27 13:20:24.295] v5.py-><module> line:253 [INFO] : MT5连接已断开
[2025-08-27 13:20:35.727] v5.py->connect_mt5 line:16 [INFO] : MT5连接成功
[2025-08-27 13:20:35.727] v5.py-><module> line:253 [INFO] : MT5连接已断开
[2025-08-27 13:21:01.213] v5.py->connect_mt5 line:16 [INFO] : MT5连接成功
[2025-08-27 13:21:01.213] v5.py-><module> line:253 [INFO] : MT5连接已断开
[2025-08-27 13:21:15.765] v5.py->connect_mt5 line:16 [INFO] : MT5连接成功
[2025-08-27 13:21:15.765] v5.py-><module> line:253 [INFO] : MT5连接已断开
[2025-08-27 13:21:25.932] v5.py->connect_mt5 line:16 [INFO] : MT5连接成功
[2025-08-27 13:21:25.933] v5.py-><module> line:253 [INFO] : MT5连接已断开
[2025-08-27 13:21:36.292] v5.py->connect_mt5 line:16 [INFO] : MT5连接成功
[2025-08-27 13:21:36.294] v5.py-><module> line:253 [INFO] : MT5连接已断开
[2025-08-27 13:21:58.739] v5.py->connect_mt5 line:16 [INFO] : MT5连接成功
[2025-08-27 13:21:58.741] v5.py-><module> line:254 [INFO] : MT5连接已断开
[2025-08-27 13:22:16.634] v5.py->connect_mt5 line:16 [INFO] : MT5连接成功
[2025-08-27 13:22:16.639] v5.py-><module> line:254 [INFO] : MT5连接已断开
[2025-08-27 13:22:26.057] v5.py->connect_mt5 line:16 [INFO] : MT5连接成功
[2025-08-27 13:22:26.058] v5.py-><module> line:254 [INFO] : MT5连接已断开
[2025-08-27 13:22:43.539] v5.py->connect_mt5 line:16 [INFO] : MT5连接成功
[2025-08-27 13:22:43.543] v5.py-><module> line:254 [INFO] : MT5连接已断开
[2025-08-27 13:23:19.096] v5.py->connect_mt5 line:16 [INFO] : MT5连接成功
[2025-08-27 13:23:19.098] v5.py-><module> line:254 [INFO] : MT5连接已断开
[2025-08-27 13:23:26.640] v5.py->connect_mt5 line:16 [INFO] : MT5连接成功
[2025-08-27 13:23:26.643] v5.py-><module> line:254 [INFO] : MT5连接已断开
[2025-08-27 13:23:40.300] v5.py->connect_mt5 line:16 [INFO] : MT5连接成功
[2025-08-27 13:23:40.301] v5.py-><module> line:254 [INFO] : MT5连接已断开
[2025-08-27 13:24:08.423] v5.py->connect_mt5 line:16 [INFO] : MT5连接成功
[2025-08-27 13:24:08.424] v5.py-><module> line:254 [INFO] : MT5连接已断开
[2025-08-27 13:24:18.590] v5.py->connect_mt5 line:16 [INFO] : MT5连接成功
[2025-08-27 13:24:18.590] v5.py-><module> line:254 [INFO] : MT5连接已断开
[2025-08-27 13:25:13.657] v5.py->connect_mt5 line:16 [INFO] : MT5连接成功
[2025-08-27 13:25:13.657] v5.py-><module> line:254 [INFO] : MT5连接已断开
[2025-08-27 13:25:22.840] v5.py->connect_mt5 line:16 [INFO] : MT5连接成功
[2025-08-27 13:25:22.840] v5.py-><module> line:254 [INFO] : MT5连接已断开
[2025-08-27 13:28:20.836] v5.py->connect_mt5 line:16 [INFO] : MT5连接成功
[2025-08-27 13:28:20.837] v5.py-><module> line:255 [INFO] : MT5连接已断开
[2025-08-27 13:28:37.157] v5.py->connect_mt5 line:16 [INFO] : MT5连接成功
[2025-08-27 13:28:37.158] v5.py-><module> line:255 [INFO] : MT5连接已断开
[2025-08-27 13:29:14.387] v5.py->connect_mt5 line:16 [INFO] : MT5连接成功
[2025-08-27 13:29:14.387] v5.py-><module> line:255 [INFO] : MT5连接已断开
[2025-08-27 13:29:49.572] v5.py->connect_mt5 line:16 [INFO] : MT5连接成功
[2025-08-27 13:29:49.573] v5.py-><module> line:255 [INFO] : MT5连接已断开
[2025-08-27 13:29:54.821] v5.py->connect_mt5 line:16 [INFO] : MT5连接成功
[2025-08-27 13:29:54.822] v5.py-><module> line:255 [INFO] : MT5连接已断开
[2025-08-27 13:30:00.326] v5.py->connect_mt5 line:16 [INFO] : MT5连接成功
[2025-08-27 13:30:00.327] v5.py-><module> line:255 [INFO] : MT5连接已断开
[2025-08-27 13:30:04.795] v5.py->connect_mt5 line:16 [INFO] : MT5连接成功
[2025-08-27 13:30:04.795] v5.py-><module> line:255 [INFO] : MT5连接已断开
[2025-08-27 13:30:09.831] v5.py->connect_mt5 line:16 [INFO] : MT5连接成功
[2025-08-27 13:30:09.832] v5.py-><module> line:255 [INFO] : MT5连接已断开
[2025-08-27 13:30:14.714] v5.py->connect_mt5 line:16 [INFO] : MT5连接成功
[2025-08-27 13:30:14.714] v5.py-><module> line:255 [INFO] : MT5连接已断开
[2025-08-27 13:30:24.258] v5.py->connect_mt5 line:16 [INFO] : MT5连接成功
[2025-08-27 13:30:24.259] v5.py-><module> line:255 [INFO] : MT5连接已断开
[2025-08-27 13:30:49.032] v5.py->connect_mt5 line:16 [INFO] : MT5连接成功
[2025-08-27 13:30:49.032] v5.py-><module> line:256 [INFO] : MT5连接已断开
[2025-08-27 13:31:06.501] v5.py->connect_mt5 line:16 [INFO] : MT5连接成功
[2025-08-27 13:31:06.502] v5.py-><module> line:256 [INFO] : MT5连接已断开
[2025-08-27 13:31:18.957] v5.py->connect_mt5 line:16 [INFO] : MT5连接成功
[2025-08-27 13:31:18.958] v5.py-><module> line:256 [INFO] : MT5连接已断开
[2025-08-27 13:31:23.808] v5.py->connect_mt5 line:16 [INFO] : MT5连接成功
[2025-08-27 13:31:23.809] v5.py-><module> line:256 [INFO] : MT5连接已断开
[2025-08-27 13:32:38.591] v5.py->connect_mt5 line:16 [INFO] : MT5连接成功
[2025-08-27 13:32:38.592] v5.py-><module> line:258 [INFO] : MT5连接已断开
[2025-08-27 13:32:48.744] v5.py->connect_mt5 line:16 [INFO] : MT5连接成功
[2025-08-27 13:32:48.745] v5.py-><module> line:258 [INFO] : MT5连接已断开
[2025-08-27 13:32:54.097] v5.py->connect_mt5 line:16 [INFO] : MT5连接成功
[2025-08-27 13:32:54.097] v5.py-><module> line:258 [INFO] : MT5连接已断开
[2025-08-27 13:45:18.268] v5.py->connect_mt5 line:16 [INFO] : MT5连接成功
[2025-08-27 13:45:18.269] v5.py-><module> line:258 [INFO] : MT5连接已断开
[2025-08-27 13:45:31.807] v5.py->connect_mt5 line:16 [INFO] : MT5连接成功
[2025-08-27 13:45:31.807] v5.py-><module> line:258 [INFO] : MT5连接已断开
[2025-08-27 13:56:17.617] v5.py->connect_mt5 line:16 [INFO] : MT5连接成功
[2025-08-27 13:56:17.618] v5.py-><module> line:259 [INFO] : MT5连接已断开
[2025-08-27 13:57:48.847] v5.py->connect_mt5 line:16 [INFO] : MT5连接成功
[2025-08-27 13:57:48.847] v5.py-><module> line:260 [INFO] : MT5连接已断开
[2025-08-27 13:58:04.053] v5.py->connect_mt5 line:16 [INFO] : MT5连接成功
[2025-08-27 13:58:04.054] v5.py-><module> line:260 [INFO] : MT5连接已断开
[2025-08-27 13:58:17.740] v5.py->connect_mt5 line:16 [INFO] : MT5连接成功
[2025-08-27 13:58:17.741] v5.py-><module> line:260 [INFO] : MT5连接已断开
[2025-08-27 13:58:29.030] v5.py->connect_mt5 line:16 [INFO] : MT5连接成功
[2025-08-27 13:58:29.031] v5.py-><module> line:260 [INFO] : MT5连接已断开
[2025-08-27 13:58:36.674] v5.py->connect_mt5 line:16 [INFO] : MT5连接成功
[2025-08-27 13:58:36.675] v5.py-><module> line:260 [INFO] : MT5连接已断开
[2025-08-27 14:00:00.508] v5.py->connect_mt5 line:16 [INFO] : MT5连接成功
[2025-08-27 14:00:00.509] v5.py-><module> line:276 [INFO] : MT5连接已断开
[2025-08-27 14:00:13.894] v5.py->connect_mt5 line:16 [INFO] : MT5连接成功
[2025-08-27 14:00:13.895] v5.py-><module> line:277 [INFO] : MT5连接已断开
[2025-08-27 14:00:54.510] v5.py->connect_mt5 line:16 [INFO] : MT5连接成功
[2025-08-27 14:00:54.511] v5.py-><module> line:285 [INFO] : MT5连接已断开
[2025-08-27 14:06:34.964] v5.py->connect_mt5 line:16 [INFO] : MT5连接成功
[2025-08-27 14:06:34.965] v5.py-><module> line:260 [INFO] : MT5连接已断开
[2025-08-27 14:12:40.927] v5.py->connect_mt5 line:16 [INFO] : MT5连接成功
[2025-08-27 14:12:40.928] v5.py-><module> line:264 [INFO] : MT5连接已断开
[2025-08-27 14:14:01.486] v5.py->connect_mt5 line:16 [INFO] : MT5连接成功
[2025-08-27 14:14:01.488] v5.py-><module> line:264 [INFO] : MT5连接已断开
[2025-08-27 14:15:07.365] v5.py->connect_mt5 line:16 [INFO] : MT5连接成功
[2025-08-27 14:15:07.366] v5.py-><module> line:264 [INFO] : MT5连接已断开
[2025-08-27 14:16:52.330] v5.py->connect_mt5 line:16 [INFO] : MT5连接成功
[2025-08-27 14:16:52.742] v5.py->open_order line:91 [INFO] : 订单成功执行，订单号: 53062502427
[2025-08-27 14:16:52.743] v5.py-><module> line:257 [INFO] : MT5连接已断开
[2025-08-27 14:17:44.666] v5.py->connect_mt5 line:16 [INFO] : MT5连接成功
[2025-08-27 14:17:45.396] v5.py->open_order line:91 [INFO] : 订单成功执行，订单号: 53062507982
[2025-08-27 14:17:45.396] v5.py-><module> line:255 [INFO] : MT5连接已断开
[2025-08-27 14:20:15.218] v5.py->connect_mt5 line:16 [INFO] : MT5连接成功
[2025-08-27 14:20:16.975] v5.py->open_order line:91 [INFO] : 订单成功执行，订单号: 53062532316
[2025-08-27 14:20:16.976] v5.py-><module> line:254 [INFO] : MT5连接已断开
[2025-08-27 14:21:30.236] v5.py->connect_mt5 line:16 [INFO] : MT5连接成功
[2025-08-27 14:21:30.237] v5.py-><module> line:255 [INFO] : MT5连接已断开
[2025-08-27 14:24:09.318] v5.py->connect_mt5 line:16 [INFO] : MT5连接成功
[2025-08-27 14:24:09.883] v5.py-><module> line:255 [INFO] : MT5连接已断开
[2025-08-27 14:25:41.393] v5.py->connect_mt5 line:16 [INFO] : MT5连接成功
[2025-08-27 14:25:41.800] v5.py->close_position line:128 [INFO] : 平仓成功，订单号: 53062480513
[2025-08-27 14:25:41.801] v5.py-><module> line:255 [INFO] : MT5连接已断开
[2025-08-27 14:29:59.273] v5.py->connect_mt5 line:16 [INFO] : MT5连接成功
[2025-08-27 14:29:59.836] v5.py->close_position line:128 [INFO] : 平仓成功，订单号: 53062317213
[2025-08-27 14:29:59.837] v5.py-><module> line:255 [INFO] : MT5连接已断开
[2025-08-27 14:36:07.932] v5.py->connect_mt5 line:14 [INFO] : MT5连接成功
[2025-08-27 14:36:07.932] v5.py->get_open_positions line:142 [INFO] : 没有当前持仓
[2025-08-27 14:36:07.932] v5.py-><module> line:222 [INFO] : MT5连接已断开
[2025-08-27 14:36:30.339] v5.py->connect_mt5 line:14 [INFO] : MT5连接成功
[2025-08-27 14:36:30.339] v5.py-><module> line:222 [INFO] : MT5连接已断开
[2025-08-27 14:36:39.976] v5.py->connect_mt5 line:14 [INFO] : MT5连接成功
[2025-08-27 14:36:39.977] v5.py-><module> line:222 [INFO] : MT5连接已断开
[2025-08-27 14:48:15.107] v5.py->connect_mt5 line:14 [INFO] : MT5连接成功
[2025-08-27 14:48:15.108] v5.py-><module> line:225 [INFO] : MT5连接已断开
[2025-08-27 14:54:10.925] v5.py->connect_mt5 line:14 [INFO] : MT5连接成功
[2025-08-27 14:54:11.521] v5.py->close_position line:126 [INFO] : 平仓成功，订单号: 53062627379
[2025-08-27 14:54:11.521] v5.py-><module> line:228 [INFO] : MT5连接已断开
[2025-08-27 14:59:44.894] v5.py->connect_mt5 line:14 [INFO] : MT5连接成功
[2025-08-27 14:59:44.894] v5.py->get_open_positions line:142 [INFO] : 没有当前持仓
[2025-08-27 14:59:44.895] v5.py-><module> line:228 [INFO] : MT5连接已断开
[2025-08-27 15:00:52.097] v5.py->connect_mt5 line:14 [INFO] : MT5连接成功
[2025-08-27 15:00:52.098] v5.py->get_open_positions line:142 [INFO] : 没有当前持仓
[2025-08-27 15:00:52.098] v5.py-><module> line:228 [INFO] : MT5连接已断开
[2025-08-27 15:01:12.466] v5.py->connect_mt5 line:14 [INFO] : MT5连接成功
[2025-08-27 15:01:12.467] v5.py->get_open_positions line:142 [INFO] : 没有当前持仓
[2025-08-27 15:01:12.468] v5.py-><module> line:228 [INFO] : MT5连接已断开
[2025-08-27 15:02:58.019] v5.py->connect_mt5 line:14 [INFO] : MT5连接成功
[2025-08-27 15:02:58.020] v5.py->get_open_positions line:142 [INFO] : 没有当前持仓
[2025-08-27 15:02:58.020] v5.py-><module> line:228 [INFO] : MT5连接已断开
[2025-08-27 15:04:42.579] v5.py->connect_mt5 line:14 [INFO] : MT5连接成功
[2025-08-27 15:04:42.580] v5.py->get_open_positions line:142 [INFO] : 没有当前持仓
[2025-08-27 15:04:42.581] v5.py-><module> line:228 [INFO] : MT5连接已断开
[2025-08-27 15:42:40.526] v5.py->connect_mt5 line:14 [INFO] : MT5连接成功
[2025-08-27 15:42:40.527] v5.py->get_open_positions line:142 [INFO] : 没有当前持仓
[2025-08-27 15:42:40.527] v5.py-><module> line:249 [INFO] : MT5连接已断开
[2025-08-27 15:42:46.943] v5.py->connect_mt5 line:14 [INFO] : MT5连接成功
[2025-08-27 15:42:46.944] v5.py->get_open_positions line:142 [INFO] : 没有当前持仓
[2025-08-27 15:42:46.944] v5.py-><module> line:249 [INFO] : MT5连接已断开
[2025-08-27 15:43:21.412] v5.py->connect_mt5 line:14 [INFO] : MT5连接成功
[2025-08-27 15:43:21.412] v5.py->get_open_positions line:142 [INFO] : 没有当前持仓
[2025-08-27 15:43:21.413] v5.py-><module> line:249 [INFO] : MT5连接已断开
[2025-08-27 15:46:07.093] v5.py->connect_mt5 line:14 [INFO] : MT5连接成功
[2025-08-27 15:46:07.093] v5.py->get_open_positions line:142 [INFO] : 没有当前持仓
[2025-08-27 15:46:07.094] v5.py-><module> line:254 [INFO] : MT5连接已断开
[2025-08-27 15:47:29.609] v5.py->connect_mt5 line:14 [INFO] : MT5连接成功
[2025-08-27 15:47:29.610] v5.py->get_open_positions line:142 [INFO] : 没有当前持仓
[2025-08-27 15:47:29.610] v5.py-><module> line:254 [INFO] : MT5连接已断开
[2025-08-27 15:47:47.593] v5.py->connect_mt5 line:14 [INFO] : MT5连接成功
[2025-08-27 15:47:47.594] v5.py->get_open_positions line:142 [INFO] : 没有当前持仓
[2025-08-27 15:47:47.594] v5.py-><module> line:254 [INFO] : MT5连接已断开
[2025-08-27 15:47:57.540] v5.py->connect_mt5 line:14 [INFO] : MT5连接成功
[2025-08-27 15:47:57.540] v5.py->get_open_positions line:142 [INFO] : 没有当前持仓
[2025-08-27 15:47:57.540] v5.py-><module> line:254 [INFO] : MT5连接已断开
[2025-08-27 15:48:31.767] v5.py->connect_mt5 line:14 [INFO] : MT5连接成功
[2025-08-27 15:48:31.767] v5.py->get_open_positions line:142 [INFO] : 没有当前持仓
[2025-08-27 15:48:31.767] v5.py-><module> line:254 [INFO] : MT5连接已断开
[2025-08-27 15:49:01.796] v5.py->connect_mt5 line:14 [INFO] : MT5连接成功
[2025-08-27 15:49:01.796] v5.py->get_open_positions line:142 [INFO] : 没有当前持仓
[2025-08-27 15:49:01.799] v5.py-><module> line:254 [INFO] : MT5连接已断开
[2025-08-27 15:49:16.824] v5.py->connect_mt5 line:14 [INFO] : MT5连接成功
[2025-08-27 15:49:16.824] v5.py->get_open_positions line:142 [INFO] : 没有当前持仓
[2025-08-27 15:49:16.824] v5.py-><module> line:254 [INFO] : MT5连接已断开
[2025-08-27 15:49:28.598] v5.py->connect_mt5 line:14 [INFO] : MT5连接成功
[2025-08-27 15:49:28.599] v5.py->get_open_positions line:142 [INFO] : 没有当前持仓
[2025-08-27 15:49:28.599] v5.py-><module> line:254 [INFO] : MT5连接已断开
[2025-08-27 15:49:35.148] v5.py->connect_mt5 line:14 [INFO] : MT5连接成功
[2025-08-27 15:49:35.148] v5.py->get_open_positions line:142 [INFO] : 没有当前持仓
[2025-08-27 15:49:35.149] v5.py-><module> line:254 [INFO] : MT5连接已断开
[2025-08-27 15:49:41.640] v5.py->connect_mt5 line:14 [INFO] : MT5连接成功
[2025-08-27 15:49:41.640] v5.py->get_open_positions line:142 [INFO] : 没有当前持仓
[2025-08-27 15:49:41.641] v5.py-><module> line:254 [INFO] : MT5连接已断开
[2025-08-27 15:49:57.295] v5.py->connect_mt5 line:14 [INFO] : MT5连接成功
[2025-08-27 15:49:57.295] v5.py->get_open_positions line:142 [INFO] : 没有当前持仓
[2025-08-27 15:49:57.296] v5.py-><module> line:254 [INFO] : MT5连接已断开
[2025-08-27 15:50:01.532] v5.py->connect_mt5 line:14 [INFO] : MT5连接成功
[2025-08-27 15:50:01.532] v5.py->get_open_positions line:142 [INFO] : 没有当前持仓
[2025-08-27 15:50:01.532] v5.py-><module> line:254 [INFO] : MT5连接已断开
[2025-08-27 15:50:28.021] v5.py->connect_mt5 line:14 [INFO] : MT5连接成功
[2025-08-27 15:50:28.021] v5.py->get_open_positions line:142 [INFO] : 没有当前持仓
[2025-08-27 15:50:28.022] v5.py-><module> line:254 [INFO] : MT5连接已断开
