from MetaTrader5 import TradePosition
from click.testing import Result

from core.common.utils.logHelper import logger
from core.platform.MetaTrader import v5


# 开仓函数
def open_order(symbol, volume, order_type, stop_loss=0, take_profit=0, deviation=10,comment='自动交易'):
    """
    发送开仓订单
    :param comment:
    :param magic:
    :param symbol: 交易品种（如"EURUSD"）
    :param volume: 交易量（手）
    :param order_type: 订单类型（mt5.ORDER_TYPE_BUY或mt5.ORDER_TYPE_SELL）
    :param stop_loss: 止损点数（可选）
    :param take_profit: 止盈点数（可选）
    :param deviation: 价格偏差（点）
    :return: 订单结果
    """
    try:
        if v5.connect_mt5():
            v5.open_order(symbol, volume, order_type, stop_loss, take_profit, deviation,comment)
        else:
            logger.error("MT5连接失败")
    except Exception as ex:
        logger.error(ex)
    finally:
        v5.close_mt5()

# 平仓函数
def close_position(position):
    return v5.close_position(position)

# 查询当前持仓
def get_open_positions(symbol=None)->list[TradePosition]:
    """
    查询当前持仓
    :param symbol: 可选，指定交易品种
    :return: 持仓列表
    """
    result = None
    try:
        if v5.connect_mt5():
            result = v5.get_open_positions(symbol)
        else:
            logger.error("MT5连接失败")
    except Exception as ex:
        logger.error(ex)
    finally:
        v5.close_mt5()
    return result


# 主函数示例
if __name__ == "__main__":
    # 连接MT5
    # v5.connect_mt5()
    v5.close_mt5()
    pass
