from typing import List,Optional

from pydantic import BaseModel, Field



class DataModel(BaseModel):
    # 操作方式
    action: str = ""
    # 合约数量
    contracts: str = ""
    # 仓位数量
    position_size: str = ""

    class Config:
        schema_extra = {
            "example": {
                "action": "{{strategy.order.action}}",
                "contracts": "{{strategy.order.contracts}}",
                "position_size": "{{strategy.position_size}}"
            }
        }


class WebhookModel(BaseModel):
    data: DataModel = DataModel()
    # 价格
    price: str = ""
    signal_param: str = ""
    # 唯一标识
    signal_type: str = ""
    # 币种
    symbol: str = ""
    # 时间
    time: str = ""

    class Config:
        schema_extra = {
            "example": {
                "data": {
                    "action": "{{strategy.order.action}}",
                    "contracts": "{{strategy.order.contracts}}",
                    "position_size": "{{strategy.position_size}}"
                },
                "price": "{{close}}",
                "signal_param": "{}",
                "signal_type": "d2acfcb9-fd86-44ce-8851-64183184652f",
                "symbol": "{{ticker}}",
                "time": "{{timenow}}"
            }
        }


class OpenModel(BaseModel):
    apiKey: str = "Your apiKey"
    apiSecretKey: str = "Your apiSecretKey"
    passPhrase: str = "Your passPhrase"
    instId: str = "产品ID，如 BTC-USD-190927-5000-C"
    side: str = "订单方向 buy：买， sell：卖"
    percent: int = 0
    sz: int = 0
    flag: str = '1:模拟'
    tdMode: str = 'cross'
    ordType: str = '订单类型 ' \
                   'market：市价单' \
                   'limit：限价单' \
                   'post_only：只做maker单' \
                   'fok：全部成交或立即取消' \
                   'ioc：立即成交并取消剩余' \
                   'optimal_limit_ioc：市价委托立即成交并取消剩余（仅适用交割、永续）' \
                   'mmp：做市商保护(仅适用于组合保证金账户模式下的期权订单)' \
                   'mmp_and_post_only：做市商保护且只做maker单(仅适用于组合保证金账户模式下的期权订单)'
    ccy: str = '保证金币种，仅适用于单币种保证金模式下的全仓杠杆订单'

    class Config:
        schema_extra = {
            "example": {
                'apiKey': 'Your apiKey',
                'apiSecretKey': 'Your apiSecretKey',
                'passPhrase': 'Your passPhrase',
                'instId': '产品ID，如 BTC-USD-190927-5000-C',
                'side': '订单方向 buy：买， sell：卖',
                'percent': 0,
                'sz': 0,
                'flag': '1:模拟',
                'tdMode': 'cross',
                'ordType': '订单类型 market：市价单limit：限价单post_only：只做maker单fok：全部成交或立即取消ioc：立即成交并取消剩余optimal_limit_ioc：市价委托立即成交并取消剩余（仅适用交割、永续）mmp：做市商保护(仅适用于组合保证金账户模式下的期权订单)mmp_and_post_only：做市商保护且只做maker单(仅适用于组合保证金账户模式下的期权订单)',
                'ccy': '保证金币种，仅适用于单币种保证金模式下的全仓杠杆订单'
            }
        }


class CloseModel(BaseModel):
    apiKey: str = "Your apiKey"
    apiSecretKey: str = "Your apiSecretKey"
    passPhrase: str = "Your passPhrase"
    flag: str = '1:模拟'
    instId: str = "产品ID，如 BTC-USD-190927-5000-C"
    mgnMode: str = '保证金模式 cross：全仓 ； isolated：逐仓'
    posSide: str = '持仓方向 买卖模式下：可不填写此参数，默认值net，如果填写，仅可以填写net 开平仓模式下： 必须填写此参数，且仅可以填写 long：平多 ，short：平空'
    ccy: str = '保证金币种，单币种保证金模式的全仓币币杠杆平仓必填'
    autoCxl: bool = '当市价全平时，平仓单是否需要自动撤销,默认为false. false：不自动撤单 true：自动撤单'

    class Config:
        schema_extra = {
            "example":
                {'apiKey': 'Your apiKey',
                 'apiSecretKey': 'Your apiSecretKey',
                 'passPhrase': 'Your passPhrase',
                 'flag': '1:模拟',
                 'instId': '产品ID，如 BTC-USD-190927-5000-C',
                 'mgnMode': '保证金模式 cross：全仓 ； isolated：逐仓',
                 'posSide': '持仓方向 买卖模式下：可不填写此参数，默认值net，如果填写，仅可以填写net 开平仓模式下： 必须填写此参数，且仅可以填写 long：平多 ，short：平空',
                 'ccy': '保证金币种，单币种保证金模式的全仓币币杠杆平仓必填',
                 'autoCxl': '当市价全平时，平仓单是否需要自动撤销,默认为false. false：不自动撤单 true：自动撤单'}
        }


if __name__ == '__main__':
    model = CloseModel()
    print(model.__dict__)
