from core.common.utils import logHelper
from core.platform.okx import Account, Trade


# 获取当前持仓信息
def getPositions(apiKey, apiSecretKey, passPhrase, flag='1', instId='', instType='', proxies=None):
    positions = []
    api = Account.AccountAPI(api_key=apiKey, api_secret_key=apiSecretKey, passphrase=passPhrase, use_server_time=False,
                             flag=flag, proxies=proxies)
    return api.get_positions()


# 平仓
def closePositions(apiKey, apiSecretKey, passPhrase, flag='1', instId='', mgnMode='cross', posSide='', ccy='',
                   autoCxl='', proxies=None):
    api = Trade.TradeAPI(api_key=apiKey, api_secret_key=apiSecretKey, passphrase=passPhrase, use_server_time=False,
                         flag=flag, proxies=proxies)
    result = api.close_positions(instId=instId, mgnMode=mgnMode, posSide=posSide, ccy=ccy, autoCxl=autoCxl)
    return result


# 获取可下单最大数量
def getMaxOrderSize(apiKey, apiSecretKey, passPhrase, instId, tdMode='cross', flag='1', proxies=None):
    api = Account.AccountAPI(api_key=apiKey, api_secret_key=apiSecretKey, passphrase=passPhrase,
                             use_server_time=False, flag=flag, proxies=proxies)
    response = api.get_max_order_size(instId=instId, tdMode=tdMode)
    if response['code'] == '0':
        data = response['data']
    else:
        data = response['msg']
    return data


# 下单
def placeOrder(apiKey, apiSecretKey, passPhrase, instId, side, percent=None, sz=None, flag='1', tdMode='cross',
               ordType='market', ccy='', proxies=None):
    api = Trade.TradeAPI(api_key=apiKey, api_secret_key=apiSecretKey, passphrase=passPhrase, use_server_time=False,
                         flag=flag, proxies=proxies)
    if percent:
        try:
            percent = float(percent) / 100
        except:
            percent = 1.00
        if percent <= 0:
            percent = 1.00
        if percent > 1.00:
            percent = 1.00
        # 获取最大开仓数量
        data = getMaxOrderSize(apiKey, apiSecretKey, passPhrase, instId, tdMode, flag, proxies)
        if type(data) is list and len(data) > 0:
            maxBuy = data[0]['maxBuy']
            maxSell = data[0]['maxSell']
            if side == 'buy':
                sz = float(maxBuy) * percent
            elif side == 'sell':
                sz = float(maxSell) * percent
            logHelper.info("maxBuy：" + str(maxBuy) + "; maxSell" + str(maxSell) + "; Count：" + str(sz))
    # sz = int(sz)
    sz2 = round(sz)
    if sz2 > sz:
        sz = sz2 - 1
    else:
        sz = sz2
    logHelper.info("Actual Count：" + str(sz))
    result = api.place_order(instId=instId, tdMode=tdMode, side=side, ordType=ordType, sz=sz, ccy=ccy)
    return result


if __name__ == '__main__':
    passphrase = 'Satan@1986'
    api_key = "8d8eb9e4-432b-4a37-9e93-d43fd93fb121"
    api_secret_key = "B933655D85559D58733D47233854FC39"
    # print(closePositions(api_key, api_secret_key, passphrase, instId='CRV-USDT-SWAP',
    #                      proxies='http://172.16.10.232:808'))
    print(getPositions(api_key, api_secret_key, passphrase, proxies='http://172.16.10.232:808'))
    # print(placeOrder(api_key, api_secret_key, passphrase,
    #                  instId='CRV-USDT-SWAP',
    #                  side='buy', percent='10', sz='100',
    #                  proxies='http://172.16.10.232:808'))
    # api1 = Account.AccountAPI(api_key=api_key, api_secret_key=api_secret_key, passphrase=passphrase,
    #                           use_server_time=False,
    #                           flag='1', proxies='http://172.16.10.232:808')
    # result1 = api1.get_max_order_size(instId='CRV-USDT-SWAP,ETH-USDT-SWAP', tdMode='cross', ccy='')
    # print(result1)
    pass
