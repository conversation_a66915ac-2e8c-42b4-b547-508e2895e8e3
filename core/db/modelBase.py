
from sqlalchemy import Column, String, text, INT, func
from sqlalchemy.dialects.mysql import BIGINT,TIMESTAMP,TINYINT,LONGTEXT
from sqlalchemy.ext.declarative import declarative_base

DeclarativeBase = declarative_base()


class ModelBase(DeclarativeBase):
    """
    基础Model模型对象
    """
    __abstract__ = True

    id = Column(INT, primary_key=True,autoincrement=True, comment='序号')
    created_time = Column(TIMESTAMP, nullable=False, default=func.current_timestamp(), comment='创建时间')
    updated_time = Column(TIMESTAMP, nullable=True,  default=func.current_timestamp(), onupdate=func.current_timestamp(), comment='更新时间')
    is_deleted = Column(INT, nullable=True, server_default=text("0"), comment='软删')

    class Config:
        orm_mode = True  # 支持从 ORM 模型实例转换

