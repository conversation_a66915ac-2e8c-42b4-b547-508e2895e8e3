import smtplib
import imaplib
import email
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart

def send_gmail(sender_email, sender_password, receiver_email, subject, message):
    """
    发送Gmail邮件

    参数:
        sender_email: 发件人邮箱
        sender_password: 发件人密码或应用专用密码
        receiver_email: 收件人邮箱
        subject: 邮件主题
        message: 邮件内容
    """
    try:
        # 创建一个多部分邮件对象
        msg = MIMEMultipart()
        msg['From'] = sender_email
        msg['To'] = receiver_email
        msg['Subject'] = subject

        # 添加邮件正文
        msg.attach(MIMEText(message, 'plain'))

        # 连接到Gmail的SMTP服务器
        with smtplib.SMTP('smtp.gmail.com', 587) as server:
            server.starttls()  # 启用TLS加密
            server.login(sender_email, sender_password)

            # 发送邮件
            text = msg.as_string()
            server.sendmail(sender_email, receiver_email, text)

        print("邮件发送成功！")
        return True
    except Exception as e:
        print(f"邮件发送失败: {str(e)}")
        return False

def receive_gmail(email_user, email_password, num_emails=5):
    """
    接收Gmail邮件

    参数:
        email_user: 邮箱账号
        email_password: 邮箱密码或应用专用密码
        num_emails: 要获取的最新邮件数量

    返回:
        包含邮件信息的列表，每个邮件是一个字典
    """
    try:
        # 连接到Gmail的IMAP服务器
        with imaplib.IMAP4_SSL('imap.gmail.com') as mail:
            mail.login(email_user, email_password)
            mail.select('inbox')  # 选择收件箱

            # 搜索所有邮件
            status, data = mail.search(None, 'ALL')
            email_ids = data[0].split()

            # 取最新的几封邮件
            emails_to_retrieve = email_ids[-num_emails:] if len(email_ids) >= num_emails else email_ids

            emails = []

            for email_id in emails_to_retrieve:
                # 获取邮件内容
                status, data = mail.fetch(email_id, '(RFC822)')
                raw_email = data[0][1]

                # 解析邮件
                msg = email.message_from_bytes(raw_email)

                # 获取发件人、主题和日期
                sender = msg['from']
                subject = msg['subject']
                date = msg['date']

                # 获取邮件正文
                body = ""
                if msg.is_multipart():
                    for part in msg.walk():
                        content_type = part.get_content_type()
                        content_disposition = str(part.get("Content-Disposition"))

                        # 忽略附件
                        if "attachment" not in content_disposition:
                            # 提取文本内容
                            if content_type == "text/plain" or content_type == "text/html":
                                try:
                                    body_part = part.get_payload(decode=True).decode()
                                    body += body_part
                                except:
                                    pass
                else:
                    # 非多部分邮件
                    content_type = msg.get_content_type()
                    if content_type == "text/plain" or content_type == "text/html":
                        body = msg.get_payload(decode=True).decode()

                emails.append({
                    'id': email_id.decode(),
                    'sender': sender,
                    'subject': subject,
                    'date': date,
                    'body': body
                })

        return emails

    except Exception as e:
        print(f"接收邮件失败: {str(e)}")
        return None

# 使用示例
if __name__ == "__main__":
    # 请替换为你的Gmail账号信息
    gmail_user = '<EMAIL>'
    gmail_password = 'cuvuwvfdvxladyin'  # 注意：对于启用2FA的账号，需要使用应用专用密码

    # 发送邮件示例
    send_success = send_gmail(
        sender_email=gmail_user,
        sender_password=gmail_password,
        receiver_email='<EMAIL>',
        subject='测试邮件',
        message='这是一封通过Python发送的测试邮件。'
    )

    # 接收邮件示例
    if send_success:
        received_emails = receive_gmail(gmail_user, gmail_password, num_emails=3)
        if received_emails:
            print("\n最新的3封邮件:")
            for idx, email in enumerate(received_emails, 1):
                print(f"\n邮件 {idx}:")
                print(f"发件人: {email['sender']}")
                print(f"主题: {email['subject']}")
                print(f"日期: {email['date']}")
                print(f"正文: {email['body'][:200]}...")  # 只显示前200个字符
