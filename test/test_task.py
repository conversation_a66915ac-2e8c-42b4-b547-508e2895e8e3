import asyncio

async def async_task(delay, value):
    print('任务已开始！')
    await asyncio.sleep(delay)
    return value * 2

async def main():
    # 启动任务但不立即等待（非阻塞）
    task = asyncio.create_task(async_task(2, 10))
    print("任务已启动，继续执行其他操作...")

    # 执行其他操作
    await asyncio.sleep(1)  # 模拟1秒的其他工作
    print("其他操作完成，开始等待任务结果...")

    # 等待任务结果
    result = await task
    print(f"任务结果：{result}")

asyncio.run(main())