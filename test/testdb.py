from sqlalchemy import create_engine

from core.common.config import Config
from core.common.utils.logHelper import logger
from core.db.modelBase import ModelBase
import business.models.email

if __name__ == '__main__':
    logger.info("Testing")
    if Config.base.env == "dev":
        engine = create_engine(Config.db.get_url())
        ModelBase.metadata.create_all(engine)
        for table in ModelBase.metadata.tables:
            logger.info(f"数据库表{table}创建成功！")
    logger.debug(Config)
    logger.debug(Config.db.get_url())