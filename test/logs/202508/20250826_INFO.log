[2025-08-26 15:57:38.749] test_mt5.py-><module> line:107 [INFO] : {'attachments': [], 'content_html': '', 'content_text': '{"idnum":"Long","data":{"action":"buy","contracts":"0.02","position_size":"0.02"},"price":"3365.130","signal_param":"{}","signal_type":"4e6f921e-1a41-48cd-8148-4e0d2eb43186","symbol":"XAUUSD","time":"2025-08-25T08:20:00Z"}\r\n', 'created_time': datetime.datetime(2025, 8, 25, 8, 20, 11), 'date': '2025-08-25T08:20:02+00:00', 'id': 11, 'is_deleted': 0, 'is_read': '0', 'is_transaction': False, 'mail_from': 'TradingView <<EMAIL>>', 'mail_to': '<EMAIL>', 'registry': <sqlalchemy.orm.decl_api.registry object at 0x000001AA0202D2B0>, 'subject': 'Alert: vp213A-XAU-30s-mv0.2', 'updated_time': datetime.datetime(2025, 8, 25, 8, 20, 11)}
[2025-08-26 15:57:38.750] test_mt5.py-><module> line:107 [INFO] : {'attachments': [], 'content_html': '', 'content_text': '{"idnum":"Long","data":{"action":"buy","contracts":"0.02","position_size":"0.02"},"price":"3364.515","signal_param":"{}","signal_type":"4e6f921e-1a41-48cd-8148-4e0d2eb43186","symbol":"XAUUSD","time":"2025-08-25T08:23:01Z"}\r\n', 'created_time': datetime.datetime(2025, 8, 25, 8, 23, 12), 'date': '2025-08-25T08:23:01+00:00', 'id': 12, 'is_deleted': 0, 'is_read': '0', 'is_transaction': False, 'mail_from': 'TradingView <<EMAIL>>', 'mail_to': '<EMAIL>', 'registry': <sqlalchemy.orm.decl_api.registry object at 0x000001AA0202D2B0>, 'subject': 'Alert: vp213A-XAU-4H-mv0.2', 'updated_time': datetime.datetime(2025, 8, 25, 8, 23, 12)}
[2025-08-26 15:57:38.750] test_mt5.py-><module> line:107 [INFO] : {'attachments': [], 'content_html': '', 'content_text': '{"idnum":"Long","data":{"action":"buy","contracts":"0.02","position_size":"0.02"},"price":"3364.030","signal_param":"{}","signal_type":"4e6f921e-1a41-48cd-8148-4e0d2eb43186","symbol":"XAUUSD","time":"2025-08-25T09:00:00Z"}\r\n', 'created_time': datetime.datetime(2025, 8, 25, 9, 1, 5), 'date': '2025-08-25T09:00:00+00:00', 'id': 13, 'is_deleted': 0, 'is_read': '0', 'is_transaction': False, 'mail_from': 'TradingView <<EMAIL>>', 'mail_to': '<EMAIL>', 'registry': <sqlalchemy.orm.decl_api.registry object at 0x000001AA0202D2B0>, 'subject': 'Alert: vp213A-XAU-30s', 'updated_time': datetime.datetime(2025, 8, 25, 9, 1, 5)}
[2025-08-26 15:57:38.750] test_mt5.py-><module> line:107 [INFO] : {'attachments': [], 'content_html': '', 'content_text': '{"idnum":"Long","data":{"action":"buy","contracts":"0.02","position_size":"0.02"},"price":"3363.885","signal_param":"{}","signal_type":"4e6f921e-1a41-48cd-8148-4e0d2eb43186","symbol":"XAUUSD","time":"2025-08-25T09:02:01Z"}\r\n', 'created_time': datetime.datetime(2025, 8, 25, 9, 2, 11), 'date': '2025-08-25T09:02:01+00:00', 'id': 14, 'is_deleted': 0, 'is_read': '0', 'is_transaction': False, 'mail_from': 'TradingView <<EMAIL>>', 'mail_to': '<EMAIL>', 'registry': <sqlalchemy.orm.decl_api.registry object at 0x000001AA0202D2B0>, 'subject': 'Alert: vp213A-XAU-30s', 'updated_time': datetime.datetime(2025, 8, 25, 9, 2, 11)}
[2025-08-26 15:57:38.751] test_mt5.py-><module> line:107 [INFO] : {'attachments': [], 'content_html': '', 'content_text': '{"idnum":"Long","data":{"action":"buy","contracts":"0.02","position_size":"0.02"},"price":"3364.115","signal_param":"{}","signal_type":"4e6f921e-1a41-48cd-8148-4e0d2eb43186","symbol":"XAUUSD","time":"2025-08-25T09:04:05Z"}\r\n', 'created_time': datetime.datetime(2025, 8, 25, 9, 4, 10), 'date': '2025-08-25T09:04:05+00:00', 'id': 15, 'is_deleted': 0, 'is_read': '0', 'is_transaction': False, 'mail_from': 'TradingView <<EMAIL>>', 'mail_to': '<EMAIL>', 'registry': <sqlalchemy.orm.decl_api.registry object at 0x000001AA0202D2B0>, 'subject': 'Alert: vp213A-XAU-1m', 'updated_time': datetime.datetime(2025, 8, 25, 9, 4, 10)}
[2025-08-26 15:57:38.751] test_mt5.py-><module> line:107 [INFO] : {'attachments': [], 'content_html': '', 'content_text': '{"idnum":"Long","data":{"action":"buy","contracts":"0.02","position_size":"0.02"},"price":"3363.595","signal_param":"{}","signal_type":"4e6f921e-1a41-48cd-8148-4e0d2eb43186","symbol":"XAUUSD","time":"2025-08-25T09:06:00Z"}\r\n', 'created_time': datetime.datetime(2025, 8, 25, 9, 6, 10), 'date': '2025-08-25T09:06:00+00:00', 'id': 16, 'is_deleted': 0, 'is_read': '0', 'is_transaction': False, 'mail_from': 'TradingView <<EMAIL>>', 'mail_to': '<EMAIL>', 'registry': <sqlalchemy.orm.decl_api.registry object at 0x000001AA0202D2B0>, 'subject': 'Alert: vp213A-XAU-30s-2', 'updated_time': datetime.datetime(2025, 8, 25, 9, 6, 10)}
[2025-08-26 15:57:38.752] test_mt5.py-><module> line:107 [INFO] : {'attachments': [], 'content_html': '', 'content_text': '{"idnum":"Long","data":{"action":"buy","contracts":"0.02","position_size":"0.02"},"price":"3362.690","signal_param":"{}","signal_type":"4e6f921e-1a41-48cd-8148-4e0d2eb43186","symbol":"XAUUSD","time":"2025-08-25T09:25:00Z"}\r\n', 'created_time': datetime.datetime(2025, 8, 25, 9, 25, 15), 'date': '2025-08-25T09:25:01+00:00', 'id': 18, 'is_deleted': 0, 'is_read': '0', 'is_transaction': False, 'mail_from': 'TradingView <<EMAIL>>', 'mail_to': '<EMAIL>', 'registry': <sqlalchemy.orm.decl_api.registry object at 0x000001AA0202D2B0>, 'subject': 'Alert: vp213A-XAU-5m', 'updated_time': datetime.datetime(2025, 8, 25, 9, 25, 15)}
[2025-08-26 15:57:38.752] test_mt5.py-><module> line:107 [INFO] : {'attachments': [], 'content_html': '', 'content_text': '{"idnum":"Long Entry","data":{"action":"buy","contracts":"14.89","position_size":"14.89"},"price":"3364.525","signal_param":"{}","signal_type":"4e6f921e-1a41-48cd-8148-4e0d2eb43186","symbol":"XAUUSD","time":"2025-08-25T13:00:00Z"}\r\n', 'created_time': datetime.datetime(2025, 8, 25, 13, 0, 14), 'date': '2025-08-25T13:00:03+00:00', 'id': 31, 'is_deleted': 0, 'is_read': '0', 'is_transaction': False, 'mail_from': 'TradingView <<EMAIL>>', 'mail_to': '<EMAIL>', 'registry': <sqlalchemy.orm.decl_api.registry object at 0x000001AA0202D2B0>, 'subject': 'Alert: vp213A-XAU-4H-mv0.2-L5.3S0.6w58r1.3d4D18A0.6n0.6-OANDA', 'updated_time': datetime.datetime(2025, 8, 25, 13, 0, 14)}
[2025-08-26 16:37:04.004] v5.py->get_open_positions line:136 [INFO] : 没有当前持仓
[2025-08-26 16:37:31.664] v5.py->get_open_positions line:136 [INFO] : 没有当前持仓
[2025-08-26 16:39:34.921] v5.py->connect_mt5 line:13 [INFO] : MT5连接成功
[2025-08-26 16:39:34.921] v5.py->get_open_positions line:139 [INFO] : 当前持仓数量: 2
[2025-08-26 16:39:34.922] v5.py->get_open_positions line:141 [INFO] : 订单号: 53055118087, 品种: EURUSD, 类型: 买入, 手数: 1.0, 开仓价: 1.16171
[2025-08-26 16:39:34.922] v5.py->get_open_positions line:141 [INFO] : 订单号: 53055120889, 品种: XAUUSD, 类型: 买入, 手数: 1.0, 开仓价: 3379.29
[2025-08-26 16:39:34.922] v5.py->close_mt5 line:18 [INFO] : MT5连接已断开
[2025-08-26 16:40:52.871] v5.py->connect_mt5 line:13 [INFO] : MT5连接成功
[2025-08-26 16:40:52.871] v5.py->get_open_positions line:139 [INFO] : 当前持仓数量: 2
[2025-08-26 16:40:52.871] v5.py->get_open_positions line:141 [INFO] : 订单号: 53055118087, 品种: EURUSD, 类型: 买入, 手数: 1.0, 开仓价: 1.16171
[2025-08-26 16:40:52.871] v5.py->get_open_positions line:141 [INFO] : 订单号: 53055120889, 品种: XAUUSD, 类型: 买入, 手数: 1.0, 开仓价: 3379.29
[2025-08-26 16:40:52.871] v5.py->close_mt5 line:18 [INFO] : MT5连接已断开
[2025-08-26 17:01:18.766] v5.py->connect_mt5 line:13 [INFO] : MT5连接成功
[2025-08-26 17:01:18.766] v5.py->get_open_positions line:139 [INFO] : 当前持仓数量: 2
[2025-08-26 17:01:18.766] v5.py->get_open_positions line:141 [INFO] : 订单号: 53055118087, 品种: EURUSD, 类型: 买入, 手数: 1.0, 开仓价: 1.16171
[2025-08-26 17:01:18.767] v5.py->get_open_positions line:141 [INFO] : 订单号: 53055120889, 品种: XAUUSD, 类型: 买入, 手数: 1.0, 开仓价: 3379.29
[2025-08-26 17:01:18.767] v5.py->close_mt5 line:18 [INFO] : MT5连接已断开
[2025-08-26 17:22:35.262] v5.py->connect_mt5 line:13 [INFO] : MT5连接成功
[2025-08-26 17:22:35.262] v5.py->get_open_positions line:140 [INFO] : 当前持仓数量: 2
[2025-08-26 17:22:35.262] v5.py->get_open_positions line:142 [INFO] : 订单号: 53055118087, 品种: EURUSD, 类型: 买入, 手数: 1.0, 开仓价: 1.16171
[2025-08-26 17:22:35.263] v5.py->get_open_positions line:142 [INFO] : 订单号: 53055120889, 品种: XAUUSD, 类型: 买入, 手数: 1.0, 开仓价: 3379.29
[2025-08-26 17:22:35.263] v5.py->close_mt5 line:18 [INFO] : MT5连接已断开
[2025-08-26 17:22:35.270] v5.py->connect_mt5 line:13 [INFO] : MT5连接成功
[2025-08-26 17:22:35.280] v5.py->close_mt5 line:18 [INFO] : MT5连接已断开
[2025-08-26 17:26:28.029] v5.py->connect_mt5 line:13 [INFO] : MT5连接成功
[2025-08-26 17:26:28.029] v5.py->get_open_positions line:140 [INFO] : 当前持仓数量: 2
[2025-08-26 17:26:28.029] v5.py->get_open_positions line:142 [INFO] : 订单号: 53055118087, 品种: EURUSD, 类型: 买入, 手数: 1.0, 开仓价: 1.16171
[2025-08-26 17:26:28.029] v5.py->get_open_positions line:142 [INFO] : 订单号: 53055120889, 品种: XAUUSD, 类型: 买入, 手数: 1.0, 开仓价: 3379.29
[2025-08-26 17:26:28.029] v5.py->close_mt5 line:18 [INFO] : MT5连接已断开
[2025-08-26 17:26:28.037] v5.py->connect_mt5 line:13 [INFO] : MT5连接成功
[2025-08-26 17:26:28.037] v5.py->close_mt5 line:18 [INFO] : MT5连接已断开
