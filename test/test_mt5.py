import MetaTrader5 as mt5
from datetime import datetime, timedelta
import pandas as pd

from business.models.ordersendresult import OrderSendResult
from core.platform.helper import mt5Helper


def initialize_mt5():
    """初始化MT5连接"""
    if not mt5.initialize():
        print(f"MT5初始化失败，错误码: {mt5.last_error()}")
        return False
    return True

def get_history_orders_with_profit(symbol=None, start_date=None, end_date=None):
    """
    兼容不同MT5版本的订单查询，解决volume属性不存在的问题
    """
    # 设置时间范围
    if end_date is None:
        end_date = datetime.now()
    if start_date is None:
        start_date = end_date - timedelta(days=30)
    start_ts = int(start_date.timestamp())
    end_ts = int(end_date.timestamp())

    # 1. 查询历史订单
    orders = mt5.history_orders_get(start_ts, end_ts, symbol=symbol)
    if not orders:
        print(f"未查询到订单，错误: {mt5.last_error()}")
        return None

    # 2. 查询同期成交记录（用于匹配盈利）
    deals = mt5.history_deals_get(start_ts, end_ts, symbol=symbol)
    order_profit_map = {}
    if deals:
        # 构建订单号到盈利的映射（处理部分成交）
        for deal in deals:
            if deal.order in order_profit_map:
                order_profit_map[deal.order] += deal.profit
            else:
                order_profit_map[deal.order] = deal.profit

    # 3. 关联订单与盈利，兼容成交量属性
    data = []
    for order in orders:
        # 解决成交量属性问题：尝试volume_initial，兼容旧版本
        try:
            # 新版本可能使用volume_initial表示初始成交量
            volume = order.volume_initial
        except AttributeError:
            #  fallback：如果没有volume_initial，尝试其他可能的字段名
            try:
                volume = order.volume
            except AttributeError:
                # 若所有字段都不存在，标记为未知
                volume = None

        # 获取盈利（未成交订单为0）
        profit = order_profit_map.get(order.ticket, 0.0)

        data.append({
            "订单号": order.ticket,
            "交易品种": order.symbol,
            "订单类型": "买入" if order.type == mt5.ORDER_TYPE_BUY else "卖出",
            "状态": order.state,
            "开仓时间": datetime.fromtimestamp(order.time_setup),
            "成交时间": datetime.fromtimestamp(order.time_done) if order.time_done > 0 else "未成交",
            "盈利": profit,
            "成交量": volume,  # 使用兼容方式获取的成交量
            "魔术数字": order.magic
        })

    return pd.DataFrame(data)



if __name__ == "__main__":
    if initialize_mt5():
        # order = mt5Helper.open_order('XAUUSD', 1.0, mt5.ORDER_TYPE_BUY, comment='测试订单')
        # print(order)
        # order_dict = order._asdict()
        # order_dict['request'] = order.request._asdict()
        # print(order_dict)
        order_dict = {'retcode': 10009, 'deal': 53012296932, 'order': 53063615673, 'volume': 1.0, 'price': 3382.78, 'bid': 0.0, 'ask': 0.0, 'comment': 'Request executed', 'request_id': 4289163627, 'retcode_external': 0, 'request': {'action': 1, 'magic': 0, 'order': 0, 'symbol': 'XAUUSD', 'volume': 1.0, 'price': 3382.78, 'stoplimit': 0.0, 'sl': 0.0, 'tp': 0.0, 'deviation': 10, 'type': 0, 'type_filling': 0, 'type_time': 0, 'expiration': 0, 'comment': '测试订单', 'position': 0, 'position_by': 0}}
        order = OrderSendResult(**order_dict)
        print(order)
        mt5.shutdown()
