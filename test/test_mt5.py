import json
from contextlib import suppress

import Meta<PERSON>rader5 as mt5
from datetime import datetime

from business.dao.emailDao import EmailDao
from business.models.email import Email
from business.schema.baseSchema import ArgsSchema, FilterSchema
from business.schema.signal import Signal
from core.common.utils.convert import json2obj
from core.common.utils.logHelper import logger
from core.platform.helper import mt5Helper


def send_market_order(symbol, volume, order_type):
    """
    发送市价单
    :param symbol: 交易品种
    :param volume: 手数
    :param order_type: 订单类型（mt5.ORDER_TYPE_BUY / mt5.ORDER_TYPE_SELL）
    """
    # 检查品种是否存在
    if not mt5.symbol_select(symbol, True):
        return f"品种{symbol}未找到"

    # 获取当前报价
    tick = mt5.symbol_info_tick(symbol)
    if not tick:
        return f"获取{symbol}报价失败"

    # 准备订单请求
    request = {
        "action": mt5.TRADE_ACTION_DEAL,
        "symbol": symbol,
        "volume": volume,
        "type": order_type,
        "price": tick.ask if order_type == mt5.ORDER_TYPE_BUY else tick.bid,
        "sl": 0.0,  # 止损（0表示不设置）
        "tp": 0.0,  # 止盈（0表示不设置）
        "deviation": 10,  # 允许滑点（点）
        "magic": 12345,  # 策略ID
        "comment": "Python市价单",
        "type_time": mt5.ORDER_TIME_GTC,  # 订单有效期（直到取消）
        "type_filling": mt5.ORDER_FILLING_FOK  # 成交方式（立即全部成交）
    }

    # 发送订单
    result = mt5.order_send(request)
    if result.retcode != mt5.TRADE_RETCODE_DONE:
        return f"订单失败，错误代码: {result.retcode}, 信息: {mt5.last_error()}"
    return f"订单成功，订单号: {result.order}"

def send_limit_order(symbol, volume, order_type, price):
    """
    发送限价单
    :param price: 挂单价格
    :param order_type: 订单类型（mt5.ORDER_TYPE_BUY_LIMIT / mt5.ORDER_TYPE_SELL_LIMIT）
    """
    if not mt5.symbol_select(symbol, True):
        return f"品种{symbol}未找到"

    request = {
        "action": mt5.TRADE_ACTION_PENDING,
        "symbol": symbol,
        "volume": volume,
        "type": order_type,
        "price": price,  # 挂单价格
        "sl": 0.0,
        "tp": 0.0,
        "deviation": 10,
        "magic": 12345,
        "comment": "Python限价单",
        "type_time": mt5.ORDER_TIME_GTC,
        "type_filling": mt5.ORDER_FILLING_FOK
    }

    result = mt5.order_send(request)
    if result.retcode != mt5.TRADE_RETCODE_DONE:
        return f"限价单失败，错误代码: {result.retcode}, 信息: {mt5.last_error()}"
    return f"限价单成功，订单号: {result.order}"

if __name__ == "__main__":
    positions = mt5Helper.get_open_positions()
    for position in positions:
        logger.debug(position)
        logger.debug(position.comment)
    mt5Helper.open_order('XAUUSD', 1.00, mt5.ORDER_TYPE_BUY,comment='自动交易')
    pass
