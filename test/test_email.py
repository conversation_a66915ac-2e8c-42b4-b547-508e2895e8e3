from business.dao.emailDao import EmailDao
from business.models.email import Email
from core.common.config import Config
from core.common.emailHelper import EmailManager
from core.common.utils.logHelper import logger

if __name__ == "__main__":
    logger.info('开始接收邮件...')
    email_manager = EmailManager(Config.email.email, Config.email.auth_code)
    emails = email_manager.receive_emails(
        mailbox="INBOX",  # 收件箱
        limit=200,
        criteria="UNSEEN"  # 只获取未读
    )

    logger.info(f'接收到【{len(emails)}】封邮件。')
    logger.info('开始处理邮件...')
    for mail in emails:
        logger.info(f'正在处理邮件:{mail}｝')
    #     # 保存邮件到数据库
    #     dao = EmailDao()
        email = Email(**mail)
    #     logger.debug(email)
    #     dao.create(email)
    #     logger.info(f'邮件已保存到数据库:{mail}｝')
        email_manager.mark_as_read(email.id)
    #     logger.info(f'邮件已标记为【已读】:{mail}｝')
    pass