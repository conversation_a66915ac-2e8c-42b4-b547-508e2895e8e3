[2025-08-26 14:27:42.988] emailService_main.py->release_resources line:16 [INFO] : EXE所在目录: D:\Code\local\tradingViewEmail\z_emailService
[2025-08-26 14:27:42.989] emailService_main.py->release_resources line:24 [INFO] : 临时资源目录: D:\Code\local\tradingViewEmail\z_emailService
[2025-08-26 14:27:43.369] emailService.py->run line:31 [INFO] : 服务启动中
[2025-08-26 14:27:43.369] email.py->email_task line:11 [INFO] : 服务启动，开始接收邮件...
[2025-08-26 14:28:06.813] emailService.py->handle_stop line:23 [INFO] : 收到停止信号，将在10秒后停止服务...
[2025-08-26 14:28:08.374] email.py->email_task line:16 [INFO] : 服务停止，邮件接收结束...
[2025-08-26 14:28:08.374] emailService.py->run line:35 [INFO] : 服务已停止
[2025-08-26 14:28:08.385] emailService_main.py-><module> line:67 [INFO] : 服务已停止
[2025-08-26 14:28:25.442] emailService_main.py->release_resources line:16 [INFO] : EXE所在目录: D:\Code\local\tradingViewEmail\z_emailService
[2025-08-26 14:28:25.442] emailService_main.py->release_resources line:24 [INFO] : 临时资源目录: D:\Code\local\tradingViewEmail\z_emailService
[2025-08-26 14:28:25.794] emailService.py->run line:31 [INFO] : 服务启动中
[2025-08-26 14:28:25.794] email.py->email_task line:11 [INFO] : 服务启动，开始接收邮件...
[2025-08-26 14:28:35.045] emailService.py->handle_stop line:23 [INFO] : 收到停止信号，将在10秒后停止服务...
[2025-08-26 14:28:35.797] email.py->email_task line:16 [INFO] : 服务停止，邮件接收结束...
[2025-08-26 14:28:35.798] emailService.py->run line:35 [INFO] : 服务已停止
[2025-08-26 14:28:35.801] emailService_main.py-><module> line:67 [INFO] : 服务已停止
[2025-08-26 14:29:55.445] emailService_main.py->release_resources line:16 [INFO] : EXE所在目录: D:\Code\local\tradingViewEmail\z_emailService
[2025-08-26 14:29:55.446] emailService_main.py->release_resources line:24 [INFO] : 临时资源目录: D:\Code\local\tradingViewEmail
[2025-08-26 14:29:55.447] emailService_main.py->release_resources line:47 [INFO] : 释放文件成功: D:\Code\local\tradingViewEmail\z_emailService\config.yml
[2025-08-26 14:29:55.447] emailService_main.py->release_resources line:47 [INFO] : 释放文件成功: D:\Code\local\tradingViewEmail\z_emailService\config_dev.yml
[2025-08-26 14:29:55.786] emailService.py->run line:31 [INFO] : 服务启动中
[2025-08-26 14:29:55.786] email.py->email_task line:11 [INFO] : 服务启动，开始接收邮件...
[2025-08-26 14:30:12.201] email.py->receive_emails line:27 [INFO] : 接收到【0】封邮件。
[2025-08-26 14:32:21.247] emailService_main.py->release_resources line:16 [INFO] : EXE所在目录: D:\Code\local\tradingViewEmail\z_emailService
[2025-08-26 14:32:21.247] emailService_main.py->release_resources line:24 [INFO] : 临时资源目录: D:\Code\local\tradingViewEmail
[2025-08-26 14:32:21.248] emailService_main.py->release_resources line:47 [INFO] : 释放文件成功: D:\Code\local\tradingViewEmail\z_emailService\config.yml
[2025-08-26 14:32:21.249] emailService_main.py->release_resources line:47 [INFO] : 释放文件成功: D:\Code\local\tradingViewEmail\z_emailService\config_dev.yml
[2025-08-26 14:32:21.659] emailService.py->run line:31 [INFO] : 服务启动中
[2025-08-26 14:32:21.659] email.py->email_task line:11 [INFO] : 服务启动，开始接收邮件...
