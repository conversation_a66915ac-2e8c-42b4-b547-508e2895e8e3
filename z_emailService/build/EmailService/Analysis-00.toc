(['D:\\Code\\local\\tradingViewEmail\\z_emailService\\emailService_main.py'],
 ['D:\\Code\\local\\tradingViewEmail\\z_emailService'],
 [],
 [('D:\\Code\\local\\tradingViewEmail\\.venv\\Lib\\site-packages\\numpy\\_pyinstaller',
   0),
  ('D:\\Code\\local\\tradingViewEmail\\.venv\\Lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks',
   -1000),
  ('D:\\Code\\local\\tradingViewEmail\\.venv\\Lib\\site-packages\\_pyinstaller_hooks_contrib',
   -1000)],
 {},
 [],
 [],
 False,
 {},
 0,
 [],
 [('config.yml',
   'D:\\Code\\local\\tradingViewEmail\\z_emailService\\config.yml',
   'DATA'),
  ('config_dev.yml',
   'D:\\Code\\local\\tradingViewEmail\\z_emailService\\config_dev.yml',
   'DATA')],
 '3.13.5 (tags/v3.13.5:6cb20a2, Jun 11 2025, 16:15:46) [MSC v.1943 64 bit '
 '(AMD64)]',
 [('pyi_rth_inspect',
   'D:\\Code\\local\\tradingViewEmail\\.venv\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_inspect.py',
   'PYSOURCE'),
  ('pyi_rth_pkgutil',
   'D:\\Code\\local\\tradingViewEmail\\.venv\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pkgutil.py',
   'PYSOURCE'),
  ('pyi_rth_multiprocessing',
   'D:\\Code\\local\\tradingViewEmail\\.venv\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_multiprocessing.py',
   'PYSOURCE'),
  ('emailService_main',
   'D:\\Code\\local\\tradingViewEmail\\z_emailService\\emailService_main.py',
   'PYSOURCE')],
 [('subprocess', 'D:\\ENVS\\Python\\Python313\\Lib\\subprocess.py', 'PYMODULE'),
  ('selectors', 'D:\\ENVS\\Python\\Python313\\Lib\\selectors.py', 'PYMODULE'),
  ('contextlib', 'D:\\ENVS\\Python\\Python313\\Lib\\contextlib.py', 'PYMODULE'),
  ('threading', 'D:\\ENVS\\Python\\Python313\\Lib\\threading.py', 'PYMODULE'),
  ('_threading_local',
   'D:\\ENVS\\Python\\Python313\\Lib\\_threading_local.py',
   'PYMODULE'),
  ('signal', 'D:\\ENVS\\Python\\Python313\\Lib\\signal.py', 'PYMODULE'),
  ('_strptime', 'D:\\ENVS\\Python\\Python313\\Lib\\_strptime.py', 'PYMODULE'),
  ('datetime', 'D:\\ENVS\\Python\\Python313\\Lib\\datetime.py', 'PYMODULE'),
  ('_pydatetime',
   'D:\\ENVS\\Python\\Python313\\Lib\\_pydatetime.py',
   'PYMODULE'),
  ('calendar', 'D:\\ENVS\\Python\\Python313\\Lib\\calendar.py', 'PYMODULE'),
  ('argparse', 'D:\\ENVS\\Python\\Python313\\Lib\\argparse.py', 'PYMODULE'),
  ('textwrap', 'D:\\ENVS\\Python\\Python313\\Lib\\textwrap.py', 'PYMODULE'),
  ('copy', 'D:\\ENVS\\Python\\Python313\\Lib\\copy.py', 'PYMODULE'),
  ('gettext', 'D:\\ENVS\\Python\\Python313\\Lib\\gettext.py', 'PYMODULE'),
  ('struct', 'D:\\ENVS\\Python\\Python313\\Lib\\struct.py', 'PYMODULE'),
  ('multiprocessing.spawn',
   'D:\\ENVS\\Python\\Python313\\Lib\\multiprocessing\\spawn.py',
   'PYMODULE'),
  ('multiprocessing.resource_tracker',
   'D:\\ENVS\\Python\\Python313\\Lib\\multiprocessing\\resource_tracker.py',
   'PYMODULE'),
  ('multiprocessing.util',
   'D:\\ENVS\\Python\\Python313\\Lib\\multiprocessing\\util.py',
   'PYMODULE'),
  ('multiprocessing.forkserver',
   'D:\\ENVS\\Python\\Python313\\Lib\\multiprocessing\\forkserver.py',
   'PYMODULE'),
  ('multiprocessing.connection',
   'D:\\ENVS\\Python\\Python313\\Lib\\multiprocessing\\connection.py',
   'PYMODULE'),
  ('multiprocessing.resource_sharer',
   'D:\\ENVS\\Python\\Python313\\Lib\\multiprocessing\\resource_sharer.py',
   'PYMODULE'),
  ('xmlrpc.client',
   'D:\\ENVS\\Python\\Python313\\Lib\\xmlrpc\\client.py',
   'PYMODULE'),
  ('xmlrpc',
   'D:\\ENVS\\Python\\Python313\\Lib\\xmlrpc\\__init__.py',
   'PYMODULE'),
  ('gzip', 'D:\\ENVS\\Python\\Python313\\Lib\\gzip.py', 'PYMODULE'),
  ('_compression',
   'D:\\ENVS\\Python\\Python313\\Lib\\_compression.py',
   'PYMODULE'),
  ('xml.parsers.expat',
   'D:\\ENVS\\Python\\Python313\\Lib\\xml\\parsers\\expat.py',
   'PYMODULE'),
  ('xml.parsers',
   'D:\\ENVS\\Python\\Python313\\Lib\\xml\\parsers\\__init__.py',
   'PYMODULE'),
  ('xml', 'D:\\ENVS\\Python\\Python313\\Lib\\xml\\__init__.py', 'PYMODULE'),
  ('xml.sax.expatreader',
   'D:\\ENVS\\Python\\Python313\\Lib\\xml\\sax\\expatreader.py',
   'PYMODULE'),
  ('xml.sax.saxutils',
   'D:\\ENVS\\Python\\Python313\\Lib\\xml\\sax\\saxutils.py',
   'PYMODULE'),
  ('urllib.request',
   'D:\\ENVS\\Python\\Python313\\Lib\\urllib\\request.py',
   'PYMODULE'),
  ('ipaddress', 'D:\\ENVS\\Python\\Python313\\Lib\\ipaddress.py', 'PYMODULE'),
  ('fnmatch', 'D:\\ENVS\\Python\\Python313\\Lib\\fnmatch.py', 'PYMODULE'),
  ('getpass', 'D:\\ENVS\\Python\\Python313\\Lib\\getpass.py', 'PYMODULE'),
  ('nturl2path', 'D:\\ENVS\\Python\\Python313\\Lib\\nturl2path.py', 'PYMODULE'),
  ('ftplib', 'D:\\ENVS\\Python\\Python313\\Lib\\ftplib.py', 'PYMODULE'),
  ('netrc', 'D:\\ENVS\\Python\\Python313\\Lib\\netrc.py', 'PYMODULE'),
  ('mimetypes', 'D:\\ENVS\\Python\\Python313\\Lib\\mimetypes.py', 'PYMODULE'),
  ('getopt', 'D:\\ENVS\\Python\\Python313\\Lib\\getopt.py', 'PYMODULE'),
  ('email.utils',
   'D:\\ENVS\\Python\\Python313\\Lib\\email\\utils.py',
   'PYMODULE'),
  ('random', 'D:\\ENVS\\Python\\Python313\\Lib\\random.py', 'PYMODULE'),
  ('statistics', 'D:\\ENVS\\Python\\Python313\\Lib\\statistics.py', 'PYMODULE'),
  ('fractions', 'D:\\ENVS\\Python\\Python313\\Lib\\fractions.py', 'PYMODULE'),
  ('numbers', 'D:\\ENVS\\Python\\Python313\\Lib\\numbers.py', 'PYMODULE'),
  ('email.charset',
   'D:\\ENVS\\Python\\Python313\\Lib\\email\\charset.py',
   'PYMODULE'),
  ('email.encoders',
   'D:\\ENVS\\Python\\Python313\\Lib\\email\\encoders.py',
   'PYMODULE'),
  ('quopri', 'D:\\ENVS\\Python\\Python313\\Lib\\quopri.py', 'PYMODULE'),
  ('email.errors',
   'D:\\ENVS\\Python\\Python313\\Lib\\email\\errors.py',
   'PYMODULE'),
  ('email.quoprimime',
   'D:\\ENVS\\Python\\Python313\\Lib\\email\\quoprimime.py',
   'PYMODULE'),
  ('email.base64mime',
   'D:\\ENVS\\Python\\Python313\\Lib\\email\\base64mime.py',
   'PYMODULE'),
  ('email._parseaddr',
   'D:\\ENVS\\Python\\Python313\\Lib\\email\\_parseaddr.py',
   'PYMODULE'),
  ('http.cookiejar',
   'D:\\ENVS\\Python\\Python313\\Lib\\http\\cookiejar.py',
   'PYMODULE'),
  ('http', 'D:\\ENVS\\Python\\Python313\\Lib\\http\\__init__.py', 'PYMODULE'),
  ('urllib',
   'D:\\ENVS\\Python\\Python313\\Lib\\urllib\\__init__.py',
   'PYMODULE'),
  ('ssl', 'D:\\ENVS\\Python\\Python313\\Lib\\ssl.py', 'PYMODULE'),
  ('urllib.response',
   'D:\\ENVS\\Python\\Python313\\Lib\\urllib\\response.py',
   'PYMODULE'),
  ('urllib.error',
   'D:\\ENVS\\Python\\Python313\\Lib\\urllib\\error.py',
   'PYMODULE'),
  ('string', 'D:\\ENVS\\Python\\Python313\\Lib\\string.py', 'PYMODULE'),
  ('hashlib', 'D:\\ENVS\\Python\\Python313\\Lib\\hashlib.py', 'PYMODULE'),
  ('email', 'D:\\ENVS\\Python\\Python313\\Lib\\email\\__init__.py', 'PYMODULE'),
  ('email.parser',
   'D:\\ENVS\\Python\\Python313\\Lib\\email\\parser.py',
   'PYMODULE'),
  ('email._policybase',
   'D:\\ENVS\\Python\\Python313\\Lib\\email\\_policybase.py',
   'PYMODULE'),
  ('email.feedparser',
   'D:\\ENVS\\Python\\Python313\\Lib\\email\\feedparser.py',
   'PYMODULE'),
  ('email.message',
   'D:\\ENVS\\Python\\Python313\\Lib\\email\\message.py',
   'PYMODULE'),
  ('email.policy',
   'D:\\ENVS\\Python\\Python313\\Lib\\email\\policy.py',
   'PYMODULE'),
  ('email.contentmanager',
   'D:\\ENVS\\Python\\Python313\\Lib\\email\\contentmanager.py',
   'PYMODULE'),
  ('email.headerregistry',
   'D:\\ENVS\\Python\\Python313\\Lib\\email\\headerregistry.py',
   'PYMODULE'),
  ('email.iterators',
   'D:\\ENVS\\Python\\Python313\\Lib\\email\\iterators.py',
   'PYMODULE'),
  ('email.generator',
   'D:\\ENVS\\Python\\Python313\\Lib\\email\\generator.py',
   'PYMODULE'),
  ('email._encoded_words',
   'D:\\ENVS\\Python\\Python313\\Lib\\email\\_encoded_words.py',
   'PYMODULE'),
  ('email._header_value_parser',
   'D:\\ENVS\\Python\\Python313\\Lib\\email\\_header_value_parser.py',
   'PYMODULE'),
  ('email.header',
   'D:\\ENVS\\Python\\Python313\\Lib\\email\\header.py',
   'PYMODULE'),
  ('bisect', 'D:\\ENVS\\Python\\Python313\\Lib\\bisect.py', 'PYMODULE'),
  ('xml.sax',
   'D:\\ENVS\\Python\\Python313\\Lib\\xml\\sax\\__init__.py',
   'PYMODULE'),
  ('xml.sax.handler',
   'D:\\ENVS\\Python\\Python313\\Lib\\xml\\sax\\handler.py',
   'PYMODULE'),
  ('xml.sax._exceptions',
   'D:\\ENVS\\Python\\Python313\\Lib\\xml\\sax\\_exceptions.py',
   'PYMODULE'),
  ('xml.sax.xmlreader',
   'D:\\ENVS\\Python\\Python313\\Lib\\xml\\sax\\xmlreader.py',
   'PYMODULE'),
  ('urllib.parse',
   'D:\\ENVS\\Python\\Python313\\Lib\\urllib\\parse.py',
   'PYMODULE'),
  ('http.client',
   'D:\\ENVS\\Python\\Python313\\Lib\\http\\client.py',
   'PYMODULE'),
  ('decimal', 'D:\\ENVS\\Python\\Python313\\Lib\\decimal.py', 'PYMODULE'),
  ('_pydecimal', 'D:\\ENVS\\Python\\Python313\\Lib\\_pydecimal.py', 'PYMODULE'),
  ('contextvars',
   'D:\\ENVS\\Python\\Python313\\Lib\\contextvars.py',
   'PYMODULE'),
  ('base64', 'D:\\ENVS\\Python\\Python313\\Lib\\base64.py', 'PYMODULE'),
  ('hmac', 'D:\\ENVS\\Python\\Python313\\Lib\\hmac.py', 'PYMODULE'),
  ('socket', 'D:\\ENVS\\Python\\Python313\\Lib\\socket.py', 'PYMODULE'),
  ('tempfile', 'D:\\ENVS\\Python\\Python313\\Lib\\tempfile.py', 'PYMODULE'),
  ('logging',
   'D:\\ENVS\\Python\\Python313\\Lib\\logging\\__init__.py',
   'PYMODULE'),
  ('pickle', 'D:\\ENVS\\Python\\Python313\\Lib\\pickle.py', 'PYMODULE'),
  ('pprint', 'D:\\ENVS\\Python\\Python313\\Lib\\pprint.py', 'PYMODULE'),
  ('dataclasses',
   'D:\\ENVS\\Python\\Python313\\Lib\\dataclasses.py',
   'PYMODULE'),
  ('inspect', 'D:\\ENVS\\Python\\Python313\\Lib\\inspect.py', 'PYMODULE'),
  ('importlib',
   'D:\\ENVS\\Python\\Python313\\Lib\\importlib\\__init__.py',
   'PYMODULE'),
  ('importlib._bootstrap_external',
   'D:\\ENVS\\Python\\Python313\\Lib\\importlib\\_bootstrap_external.py',
   'PYMODULE'),
  ('importlib.metadata',
   'D:\\ENVS\\Python\\Python313\\Lib\\importlib\\metadata\\__init__.py',
   'PYMODULE'),
  ('csv', 'D:\\ENVS\\Python\\Python313\\Lib\\csv.py', 'PYMODULE'),
  ('importlib.metadata._adapters',
   'D:\\ENVS\\Python\\Python313\\Lib\\importlib\\metadata\\_adapters.py',
   'PYMODULE'),
  ('importlib.metadata._text',
   'D:\\ENVS\\Python\\Python313\\Lib\\importlib\\metadata\\_text.py',
   'PYMODULE'),
  ('typing', 'D:\\ENVS\\Python\\Python313\\Lib\\typing.py', 'PYMODULE'),
  ('importlib.abc',
   'D:\\ENVS\\Python\\Python313\\Lib\\importlib\\abc.py',
   'PYMODULE'),
  ('importlib.resources.abc',
   'D:\\ENVS\\Python\\Python313\\Lib\\importlib\\resources\\abc.py',
   'PYMODULE'),
  ('importlib.resources',
   'D:\\ENVS\\Python\\Python313\\Lib\\importlib\\resources\\__init__.py',
   'PYMODULE'),
  ('importlib.resources._functional',
   'D:\\ENVS\\Python\\Python313\\Lib\\importlib\\resources\\_functional.py',
   'PYMODULE'),
  ('importlib.resources._common',
   'D:\\ENVS\\Python\\Python313\\Lib\\importlib\\resources\\_common.py',
   'PYMODULE'),
  ('importlib.resources._adapters',
   'D:\\ENVS\\Python\\Python313\\Lib\\importlib\\resources\\_adapters.py',
   'PYMODULE'),
  ('importlib._abc',
   'D:\\ENVS\\Python\\Python313\\Lib\\importlib\\_abc.py',
   'PYMODULE'),
  ('importlib.metadata._itertools',
   'D:\\ENVS\\Python\\Python313\\Lib\\importlib\\metadata\\_itertools.py',
   'PYMODULE'),
  ('importlib.metadata._functools',
   'D:\\ENVS\\Python\\Python313\\Lib\\importlib\\metadata\\_functools.py',
   'PYMODULE'),
  ('importlib.metadata._collections',
   'D:\\ENVS\\Python\\Python313\\Lib\\importlib\\metadata\\_collections.py',
   'PYMODULE'),
  ('importlib.metadata._meta',
   'D:\\ENVS\\Python\\Python313\\Lib\\importlib\\metadata\\_meta.py',
   'PYMODULE'),
  ('zipfile',
   'D:\\ENVS\\Python\\Python313\\Lib\\zipfile\\__init__.py',
   'PYMODULE'),
  ('zipfile._path',
   'D:\\ENVS\\Python\\Python313\\Lib\\zipfile\\_path\\__init__.py',
   'PYMODULE'),
  ('zipfile._path.glob',
   'D:\\ENVS\\Python\\Python313\\Lib\\zipfile\\_path\\glob.py',
   'PYMODULE'),
  ('py_compile', 'D:\\ENVS\\Python\\Python313\\Lib\\py_compile.py', 'PYMODULE'),
  ('lzma', 'D:\\ENVS\\Python\\Python313\\Lib\\lzma.py', 'PYMODULE'),
  ('bz2', 'D:\\ENVS\\Python\\Python313\\Lib\\bz2.py', 'PYMODULE'),
  ('importlib.util',
   'D:\\ENVS\\Python\\Python313\\Lib\\importlib\\util.py',
   'PYMODULE'),
  ('pathlib',
   'D:\\ENVS\\Python\\Python313\\Lib\\pathlib\\__init__.py',
   'PYMODULE'),
  ('pathlib._local',
   'D:\\ENVS\\Python\\Python313\\Lib\\pathlib\\_local.py',
   'PYMODULE'),
  ('glob', 'D:\\ENVS\\Python\\Python313\\Lib\\glob.py', 'PYMODULE'),
  ('pathlib._abc',
   'D:\\ENVS\\Python\\Python313\\Lib\\pathlib\\_abc.py',
   'PYMODULE'),
  ('json', 'D:\\ENVS\\Python\\Python313\\Lib\\json\\__init__.py', 'PYMODULE'),
  ('json.encoder',
   'D:\\ENVS\\Python\\Python313\\Lib\\json\\encoder.py',
   'PYMODULE'),
  ('json.decoder',
   'D:\\ENVS\\Python\\Python313\\Lib\\json\\decoder.py',
   'PYMODULE'),
  ('json.scanner',
   'D:\\ENVS\\Python\\Python313\\Lib\\json\\scanner.py',
   'PYMODULE'),
  ('__future__', 'D:\\ENVS\\Python\\Python313\\Lib\\__future__.py', 'PYMODULE'),
  ('importlib.readers',
   'D:\\ENVS\\Python\\Python313\\Lib\\importlib\\readers.py',
   'PYMODULE'),
  ('importlib.resources.readers',
   'D:\\ENVS\\Python\\Python313\\Lib\\importlib\\resources\\readers.py',
   'PYMODULE'),
  ('importlib.resources._itertools',
   'D:\\ENVS\\Python\\Python313\\Lib\\importlib\\resources\\_itertools.py',
   'PYMODULE'),
  ('importlib._bootstrap',
   'D:\\ENVS\\Python\\Python313\\Lib\\importlib\\_bootstrap.py',
   'PYMODULE'),
  ('token', 'D:\\ENVS\\Python\\Python313\\Lib\\token.py', 'PYMODULE'),
  ('tokenize', 'D:\\ENVS\\Python\\Python313\\Lib\\tokenize.py', 'PYMODULE'),
  ('importlib.machinery',
   'D:\\ENVS\\Python\\Python313\\Lib\\importlib\\machinery.py',
   'PYMODULE'),
  ('dis', 'D:\\ENVS\\Python\\Python313\\Lib\\dis.py', 'PYMODULE'),
  ('opcode', 'D:\\ENVS\\Python\\Python313\\Lib\\opcode.py', 'PYMODULE'),
  ('_opcode_metadata',
   'D:\\ENVS\\Python\\Python313\\Lib\\_opcode_metadata.py',
   'PYMODULE'),
  ('ast', 'D:\\ENVS\\Python\\Python313\\Lib\\ast.py', 'PYMODULE'),
  ('_compat_pickle',
   'D:\\ENVS\\Python\\Python313\\Lib\\_compat_pickle.py',
   'PYMODULE'),
  ('multiprocessing.context',
   'D:\\ENVS\\Python\\Python313\\Lib\\multiprocessing\\context.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_win32',
   'D:\\ENVS\\Python\\Python313\\Lib\\multiprocessing\\popen_spawn_win32.py',
   'PYMODULE'),
  ('multiprocessing.popen_forkserver',
   'D:\\ENVS\\Python\\Python313\\Lib\\multiprocessing\\popen_forkserver.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_posix',
   'D:\\ENVS\\Python\\Python313\\Lib\\multiprocessing\\popen_spawn_posix.py',
   'PYMODULE'),
  ('multiprocessing.popen_fork',
   'D:\\ENVS\\Python\\Python313\\Lib\\multiprocessing\\popen_fork.py',
   'PYMODULE'),
  ('multiprocessing.sharedctypes',
   'D:\\ENVS\\Python\\Python313\\Lib\\multiprocessing\\sharedctypes.py',
   'PYMODULE'),
  ('multiprocessing.heap',
   'D:\\ENVS\\Python\\Python313\\Lib\\multiprocessing\\heap.py',
   'PYMODULE'),
  ('ctypes',
   'D:\\ENVS\\Python\\Python313\\Lib\\ctypes\\__init__.py',
   'PYMODULE'),
  ('ctypes._endian',
   'D:\\ENVS\\Python\\Python313\\Lib\\ctypes\\_endian.py',
   'PYMODULE'),
  ('multiprocessing.pool',
   'D:\\ENVS\\Python\\Python313\\Lib\\multiprocessing\\pool.py',
   'PYMODULE'),
  ('multiprocessing.dummy',
   'D:\\ENVS\\Python\\Python313\\Lib\\multiprocessing\\dummy\\__init__.py',
   'PYMODULE'),
  ('multiprocessing.dummy.connection',
   'D:\\ENVS\\Python\\Python313\\Lib\\multiprocessing\\dummy\\connection.py',
   'PYMODULE'),
  ('queue', 'D:\\ENVS\\Python\\Python313\\Lib\\queue.py', 'PYMODULE'),
  ('multiprocessing.queues',
   'D:\\ENVS\\Python\\Python313\\Lib\\multiprocessing\\queues.py',
   'PYMODULE'),
  ('multiprocessing.synchronize',
   'D:\\ENVS\\Python\\Python313\\Lib\\multiprocessing\\synchronize.py',
   'PYMODULE'),
  ('multiprocessing.managers',
   'D:\\ENVS\\Python\\Python313\\Lib\\multiprocessing\\managers.py',
   'PYMODULE'),
  ('multiprocessing.shared_memory',
   'D:\\ENVS\\Python\\Python313\\Lib\\multiprocessing\\shared_memory.py',
   'PYMODULE'),
  ('secrets', 'D:\\ENVS\\Python\\Python313\\Lib\\secrets.py', 'PYMODULE'),
  ('multiprocessing.reduction',
   'D:\\ENVS\\Python\\Python313\\Lib\\multiprocessing\\reduction.py',
   'PYMODULE'),
  ('multiprocessing.process',
   'D:\\ENVS\\Python\\Python313\\Lib\\multiprocessing\\process.py',
   'PYMODULE'),
  ('runpy', 'D:\\ENVS\\Python\\Python313\\Lib\\runpy.py', 'PYMODULE'),
  ('pkgutil', 'D:\\ENVS\\Python\\Python313\\Lib\\pkgutil.py', 'PYMODULE'),
  ('zipimport', 'D:\\ENVS\\Python\\Python313\\Lib\\zipimport.py', 'PYMODULE'),
  ('multiprocessing',
   'D:\\ENVS\\Python\\Python313\\Lib\\multiprocessing\\__init__.py',
   'PYMODULE'),
  ('_py_abc', 'D:\\ENVS\\Python\\Python313\\Lib\\_py_abc.py', 'PYMODULE'),
  ('stringprep', 'D:\\ENVS\\Python\\Python313\\Lib\\stringprep.py', 'PYMODULE'),
  ('_colorize', 'D:\\ENVS\\Python\\Python313\\Lib\\_colorize.py', 'PYMODULE'),
  ('tracemalloc',
   'D:\\ENVS\\Python\\Python313\\Lib\\tracemalloc.py',
   'PYMODULE'),
  ('shutil', 'D:\\ENVS\\Python\\Python313\\Lib\\shutil.py', 'PYMODULE'),
  ('tarfile', 'D:\\ENVS\\Python\\Python313\\Lib\\tarfile.py', 'PYMODULE'),
  ('asyncio',
   'D:\\ENVS\\Python\\Python313\\Lib\\asyncio\\__init__.py',
   'PYMODULE'),
  ('asyncio.unix_events',
   'D:\\ENVS\\Python\\Python313\\Lib\\asyncio\\unix_events.py',
   'PYMODULE'),
  ('asyncio.log',
   'D:\\ENVS\\Python\\Python313\\Lib\\asyncio\\log.py',
   'PYMODULE'),
  ('asyncio.windows_events',
   'D:\\ENVS\\Python\\Python313\\Lib\\asyncio\\windows_events.py',
   'PYMODULE'),
  ('asyncio.windows_utils',
   'D:\\ENVS\\Python\\Python313\\Lib\\asyncio\\windows_utils.py',
   'PYMODULE'),
  ('asyncio.selector_events',
   'D:\\ENVS\\Python\\Python313\\Lib\\asyncio\\selector_events.py',
   'PYMODULE'),
  ('asyncio.proactor_events',
   'D:\\ENVS\\Python\\Python313\\Lib\\asyncio\\proactor_events.py',
   'PYMODULE'),
  ('asyncio.base_subprocess',
   'D:\\ENVS\\Python\\Python313\\Lib\\asyncio\\base_subprocess.py',
   'PYMODULE'),
  ('asyncio.threads',
   'D:\\ENVS\\Python\\Python313\\Lib\\asyncio\\threads.py',
   'PYMODULE'),
  ('asyncio.taskgroups',
   'D:\\ENVS\\Python\\Python313\\Lib\\asyncio\\taskgroups.py',
   'PYMODULE'),
  ('asyncio.subprocess',
   'D:\\ENVS\\Python\\Python313\\Lib\\asyncio\\subprocess.py',
   'PYMODULE'),
  ('asyncio.streams',
   'D:\\ENVS\\Python\\Python313\\Lib\\asyncio\\streams.py',
   'PYMODULE'),
  ('asyncio.runners',
   'D:\\ENVS\\Python\\Python313\\Lib\\asyncio\\runners.py',
   'PYMODULE'),
  ('asyncio.base_events',
   'D:\\ENVS\\Python\\Python313\\Lib\\asyncio\\base_events.py',
   'PYMODULE'),
  ('concurrent.futures',
   'D:\\ENVS\\Python\\Python313\\Lib\\concurrent\\futures\\__init__.py',
   'PYMODULE'),
  ('concurrent.futures.thread',
   'D:\\ENVS\\Python\\Python313\\Lib\\concurrent\\futures\\thread.py',
   'PYMODULE'),
  ('concurrent.futures.process',
   'D:\\ENVS\\Python\\Python313\\Lib\\concurrent\\futures\\process.py',
   'PYMODULE'),
  ('concurrent.futures._base',
   'D:\\ENVS\\Python\\Python313\\Lib\\concurrent\\futures\\_base.py',
   'PYMODULE'),
  ('concurrent',
   'D:\\ENVS\\Python\\Python313\\Lib\\concurrent\\__init__.py',
   'PYMODULE'),
  ('asyncio.trsock',
   'D:\\ENVS\\Python\\Python313\\Lib\\asyncio\\trsock.py',
   'PYMODULE'),
  ('asyncio.staggered',
   'D:\\ENVS\\Python\\Python313\\Lib\\asyncio\\staggered.py',
   'PYMODULE'),
  ('asyncio.timeouts',
   'D:\\ENVS\\Python\\Python313\\Lib\\asyncio\\timeouts.py',
   'PYMODULE'),
  ('asyncio.tasks',
   'D:\\ENVS\\Python\\Python313\\Lib\\asyncio\\tasks.py',
   'PYMODULE'),
  ('asyncio.queues',
   'D:\\ENVS\\Python\\Python313\\Lib\\asyncio\\queues.py',
   'PYMODULE'),
  ('asyncio.base_tasks',
   'D:\\ENVS\\Python\\Python313\\Lib\\asyncio\\base_tasks.py',
   'PYMODULE'),
  ('asyncio.locks',
   'D:\\ENVS\\Python\\Python313\\Lib\\asyncio\\locks.py',
   'PYMODULE'),
  ('asyncio.mixins',
   'D:\\ENVS\\Python\\Python313\\Lib\\asyncio\\mixins.py',
   'PYMODULE'),
  ('asyncio.sslproto',
   'D:\\ENVS\\Python\\Python313\\Lib\\asyncio\\sslproto.py',
   'PYMODULE'),
  ('asyncio.transports',
   'D:\\ENVS\\Python\\Python313\\Lib\\asyncio\\transports.py',
   'PYMODULE'),
  ('asyncio.protocols',
   'D:\\ENVS\\Python\\Python313\\Lib\\asyncio\\protocols.py',
   'PYMODULE'),
  ('asyncio.futures',
   'D:\\ENVS\\Python\\Python313\\Lib\\asyncio\\futures.py',
   'PYMODULE'),
  ('asyncio.base_futures',
   'D:\\ENVS\\Python\\Python313\\Lib\\asyncio\\base_futures.py',
   'PYMODULE'),
  ('asyncio.exceptions',
   'D:\\ENVS\\Python\\Python313\\Lib\\asyncio\\exceptions.py',
   'PYMODULE'),
  ('asyncio.events',
   'D:\\ENVS\\Python\\Python313\\Lib\\asyncio\\events.py',
   'PYMODULE'),
  ('asyncio.format_helpers',
   'D:\\ENVS\\Python\\Python313\\Lib\\asyncio\\format_helpers.py',
   'PYMODULE'),
  ('asyncio.coroutines',
   'D:\\ENVS\\Python\\Python313\\Lib\\asyncio\\coroutines.py',
   'PYMODULE'),
  ('asyncio.constants',
   'D:\\ENVS\\Python\\Python313\\Lib\\asyncio\\constants.py',
   'PYMODULE')],
 [('python313.dll', 'D:\\ENVS\\Python\\Python313\\python313.dll', 'BINARY'),
  ('select.pyd', 'D:\\ENVS\\Python\\Python313\\DLLs\\select.pyd', 'EXTENSION'),
  ('_multiprocessing.pyd',
   'D:\\ENVS\\Python\\Python313\\DLLs\\_multiprocessing.pyd',
   'EXTENSION'),
  ('pyexpat.pyd',
   'D:\\ENVS\\Python\\Python313\\DLLs\\pyexpat.pyd',
   'EXTENSION'),
  ('_ssl.pyd', 'D:\\ENVS\\Python\\Python313\\DLLs\\_ssl.pyd', 'EXTENSION'),
  ('_hashlib.pyd',
   'D:\\ENVS\\Python\\Python313\\DLLs\\_hashlib.pyd',
   'EXTENSION'),
  ('unicodedata.pyd',
   'D:\\ENVS\\Python\\Python313\\DLLs\\unicodedata.pyd',
   'EXTENSION'),
  ('_decimal.pyd',
   'D:\\ENVS\\Python\\Python313\\DLLs\\_decimal.pyd',
   'EXTENSION'),
  ('_socket.pyd',
   'D:\\ENVS\\Python\\Python313\\DLLs\\_socket.pyd',
   'EXTENSION'),
  ('_lzma.pyd', 'D:\\ENVS\\Python\\Python313\\DLLs\\_lzma.pyd', 'EXTENSION'),
  ('_bz2.pyd', 'D:\\ENVS\\Python\\Python313\\DLLs\\_bz2.pyd', 'EXTENSION'),
  ('_ctypes.pyd',
   'D:\\ENVS\\Python\\Python313\\DLLs\\_ctypes.pyd',
   'EXTENSION'),
  ('_queue.pyd', 'D:\\ENVS\\Python\\Python313\\DLLs\\_queue.pyd', 'EXTENSION'),
  ('_overlapped.pyd',
   'D:\\ENVS\\Python\\Python313\\DLLs\\_overlapped.pyd',
   'EXTENSION'),
  ('_asyncio.pyd',
   'D:\\ENVS\\Python\\Python313\\DLLs\\_asyncio.pyd',
   'EXTENSION'),
  ('api-ms-win-crt-convert-l1-1-0.dll',
   'C:\\Windows\\system32\\api-ms-win-crt-convert-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-stdio-l1-1-0.dll',
   'C:\\Windows\\system32\\api-ms-win-crt-stdio-l1-1-0.dll',
   'BINARY'),
  ('VCRUNTIME140.dll',
   'D:\\ENVS\\Python\\Python313\\VCRUNTIME140.dll',
   'BINARY'),
  ('api-ms-win-crt-time-l1-1-0.dll',
   'C:\\Windows\\system32\\api-ms-win-crt-time-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-string-l1-1-0.dll',
   'C:\\Windows\\system32\\api-ms-win-crt-string-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-conio-l1-1-0.dll',
   'C:\\Windows\\system32\\api-ms-win-crt-conio-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-locale-l1-1-0.dll',
   'C:\\Windows\\system32\\api-ms-win-crt-locale-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-process-l1-1-0.dll',
   'C:\\Windows\\system32\\api-ms-win-crt-process-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-heap-l1-1-0.dll',
   'C:\\Windows\\system32\\api-ms-win-crt-heap-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-math-l1-1-0.dll',
   'C:\\Windows\\system32\\api-ms-win-crt-math-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-filesystem-l1-1-0.dll',
   'C:\\Windows\\system32\\api-ms-win-crt-filesystem-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-runtime-l1-1-0.dll',
   'C:\\Windows\\system32\\api-ms-win-crt-runtime-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-environment-l1-1-0.dll',
   'C:\\Windows\\system32\\api-ms-win-crt-environment-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-utility-l1-1-0.dll',
   'C:\\Windows\\system32\\api-ms-win-crt-utility-l1-1-0.dll',
   'BINARY'),
  ('libssl-3.dll', 'D:\\ENVS\\Python\\Python313\\DLLs\\libssl-3.dll', 'BINARY'),
  ('libcrypto-3.dll',
   'D:\\ENVS\\Python\\Python313\\DLLs\\libcrypto-3.dll',
   'BINARY'),
  ('libffi-8.dll', 'D:\\ENVS\\Python\\Python313\\DLLs\\libffi-8.dll', 'BINARY'),
  ('ucrtbase.dll', 'C:\\Windows\\system32\\ucrtbase.dll', 'BINARY'),
  ('api-ms-win-core-debug-l1-1-0.dll',
   'C:\\Windows\\system32\\api-ms-win-core-debug-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-sysinfo-l1-1-0.dll',
   'C:\\Windows\\system32\\api-ms-win-core-sysinfo-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l1-2-0.dll',
   'C:\\Windows\\system32\\api-ms-win-core-file-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-synch-l1-1-0.dll',
   'C:\\Windows\\system32\\api-ms-win-core-synch-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-localization-l1-2-0.dll',
   'C:\\Windows\\system32\\api-ms-win-core-localization-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l1-1-0.dll',
   'C:\\Windows\\system32\\api-ms-win-core-file-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l2-1-0.dll',
   'C:\\Windows\\system32\\api-ms-win-core-file-l2-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-handle-l1-1-0.dll',
   'C:\\Windows\\system32\\api-ms-win-core-handle-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-datetime-l1-1-0.dll',
   'C:\\Windows\\system32\\api-ms-win-core-datetime-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-profile-l1-1-0.dll',
   'C:\\Windows\\system32\\api-ms-win-core-profile-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-interlocked-l1-1-0.dll',
   'C:\\Windows\\system32\\api-ms-win-core-interlocked-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-string-l1-1-0.dll',
   'C:\\Windows\\system32\\api-ms-win-core-string-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processthreads-l1-1-0.dll',
   'C:\\Windows\\system32\\api-ms-win-core-processthreads-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-rtlsupport-l1-1-0.dll',
   'C:\\Windows\\system32\\api-ms-win-core-rtlsupport-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processenvironment-l1-1-0.dll',
   'C:\\Windows\\system32\\api-ms-win-core-processenvironment-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-util-l1-1-0.dll',
   'C:\\Windows\\system32\\api-ms-win-core-util-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processthreads-l1-1-1.dll',
   'C:\\Windows\\system32\\api-ms-win-core-processthreads-l1-1-1.dll',
   'BINARY'),
  ('api-ms-win-core-timezone-l1-1-0.dll',
   'C:\\Windows\\system32\\api-ms-win-core-timezone-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-heap-l1-1-0.dll',
   'C:\\Windows\\system32\\api-ms-win-core-heap-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-libraryloader-l1-1-0.dll',
   'C:\\Windows\\system32\\api-ms-win-core-libraryloader-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-memory-l1-1-0.dll',
   'C:\\Windows\\system32\\api-ms-win-core-memory-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-synch-l1-2-0.dll',
   'C:\\Windows\\system32\\api-ms-win-core-synch-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-namedpipe-l1-1-0.dll',
   'C:\\Windows\\system32\\api-ms-win-core-namedpipe-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-console-l1-1-0.dll',
   'C:\\Windows\\system32\\api-ms-win-core-console-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-errorhandling-l1-1-0.dll',
   'C:\\Windows\\system32\\api-ms-win-core-errorhandling-l1-1-0.dll',
   'BINARY')],
 [],
 [],
 [('config.yml',
   'D:\\Code\\local\\tradingViewEmail\\z_emailService\\config.yml',
   'DATA'),
  ('config_dev.yml',
   'D:\\Code\\local\\tradingViewEmail\\z_emailService\\config_dev.yml',
   'DATA'),
  ('base_library.zip',
   'D:\\Code\\local\\tradingViewEmail\\z_emailService\\build\\EmailService\\base_library.zip',
   'DATA')],
 [('codecs', 'D:\\ENVS\\Python\\Python313\\Lib\\codecs.py', 'PYMODULE'),
  ('linecache', 'D:\\ENVS\\Python\\Python313\\Lib\\linecache.py', 'PYMODULE'),
  ('posixpath', 'D:\\ENVS\\Python\\Python313\\Lib\\posixpath.py', 'PYMODULE'),
  ('stat', 'D:\\ENVS\\Python\\Python313\\Lib\\stat.py', 'PYMODULE'),
  ('types', 'D:\\ENVS\\Python\\Python313\\Lib\\types.py', 'PYMODULE'),
  ('_collections_abc',
   'D:\\ENVS\\Python\\Python313\\Lib\\_collections_abc.py',
   'PYMODULE'),
  ('re._parser',
   'D:\\ENVS\\Python\\Python313\\Lib\\re\\_parser.py',
   'PYMODULE'),
  ('re._constants',
   'D:\\ENVS\\Python\\Python313\\Lib\\re\\_constants.py',
   'PYMODULE'),
  ('re._compiler',
   'D:\\ENVS\\Python\\Python313\\Lib\\re\\_compiler.py',
   'PYMODULE'),
  ('re._casefix',
   'D:\\ENVS\\Python\\Python313\\Lib\\re\\_casefix.py',
   'PYMODULE'),
  ('re', 'D:\\ENVS\\Python\\Python313\\Lib\\re\\__init__.py', 'PYMODULE'),
  ('sre_constants',
   'D:\\ENVS\\Python\\Python313\\Lib\\sre_constants.py',
   'PYMODULE'),
  ('copyreg', 'D:\\ENVS\\Python\\Python313\\Lib\\copyreg.py', 'PYMODULE'),
  ('locale', 'D:\\ENVS\\Python\\Python313\\Lib\\locale.py', 'PYMODULE'),
  ('operator', 'D:\\ENVS\\Python\\Python313\\Lib\\operator.py', 'PYMODULE'),
  ('enum', 'D:\\ENVS\\Python\\Python313\\Lib\\enum.py', 'PYMODULE'),
  ('_weakrefset',
   'D:\\ENVS\\Python\\Python313\\Lib\\_weakrefset.py',
   'PYMODULE'),
  ('sre_parse', 'D:\\ENVS\\Python\\Python313\\Lib\\sre_parse.py', 'PYMODULE'),
  ('keyword', 'D:\\ENVS\\Python\\Python313\\Lib\\keyword.py', 'PYMODULE'),
  ('genericpath',
   'D:\\ENVS\\Python\\Python313\\Lib\\genericpath.py',
   'PYMODULE'),
  ('abc', 'D:\\ENVS\\Python\\Python313\\Lib\\abc.py', 'PYMODULE'),
  ('io', 'D:\\ENVS\\Python\\Python313\\Lib\\io.py', 'PYMODULE'),
  ('ntpath', 'D:\\ENVS\\Python\\Python313\\Lib\\ntpath.py', 'PYMODULE'),
  ('collections',
   'D:\\ENVS\\Python\\Python313\\Lib\\collections\\__init__.py',
   'PYMODULE'),
  ('heapq', 'D:\\ENVS\\Python\\Python313\\Lib\\heapq.py', 'PYMODULE'),
  ('sre_compile',
   'D:\\ENVS\\Python\\Python313\\Lib\\sre_compile.py',
   'PYMODULE'),
  ('encodings.zlib_codec',
   'D:\\ENVS\\Python\\Python313\\Lib\\encodings\\zlib_codec.py',
   'PYMODULE'),
  ('encodings.uu_codec',
   'D:\\ENVS\\Python\\Python313\\Lib\\encodings\\uu_codec.py',
   'PYMODULE'),
  ('encodings.utf_8_sig',
   'D:\\ENVS\\Python\\Python313\\Lib\\encodings\\utf_8_sig.py',
   'PYMODULE'),
  ('encodings.utf_8',
   'D:\\ENVS\\Python\\Python313\\Lib\\encodings\\utf_8.py',
   'PYMODULE'),
  ('encodings.utf_7',
   'D:\\ENVS\\Python\\Python313\\Lib\\encodings\\utf_7.py',
   'PYMODULE'),
  ('encodings.utf_32_le',
   'D:\\ENVS\\Python\\Python313\\Lib\\encodings\\utf_32_le.py',
   'PYMODULE'),
  ('encodings.utf_32_be',
   'D:\\ENVS\\Python\\Python313\\Lib\\encodings\\utf_32_be.py',
   'PYMODULE'),
  ('encodings.utf_32',
   'D:\\ENVS\\Python\\Python313\\Lib\\encodings\\utf_32.py',
   'PYMODULE'),
  ('encodings.utf_16_le',
   'D:\\ENVS\\Python\\Python313\\Lib\\encodings\\utf_16_le.py',
   'PYMODULE'),
  ('encodings.utf_16_be',
   'D:\\ENVS\\Python\\Python313\\Lib\\encodings\\utf_16_be.py',
   'PYMODULE'),
  ('encodings.utf_16',
   'D:\\ENVS\\Python\\Python313\\Lib\\encodings\\utf_16.py',
   'PYMODULE'),
  ('encodings.unicode_escape',
   'D:\\ENVS\\Python\\Python313\\Lib\\encodings\\unicode_escape.py',
   'PYMODULE'),
  ('encodings.undefined',
   'D:\\ENVS\\Python\\Python313\\Lib\\encodings\\undefined.py',
   'PYMODULE'),
  ('encodings.tis_620',
   'D:\\ENVS\\Python\\Python313\\Lib\\encodings\\tis_620.py',
   'PYMODULE'),
  ('encodings.shift_jisx0213',
   'D:\\ENVS\\Python\\Python313\\Lib\\encodings\\shift_jisx0213.py',
   'PYMODULE'),
  ('encodings.shift_jis_2004',
   'D:\\ENVS\\Python\\Python313\\Lib\\encodings\\shift_jis_2004.py',
   'PYMODULE'),
  ('encodings.shift_jis',
   'D:\\ENVS\\Python\\Python313\\Lib\\encodings\\shift_jis.py',
   'PYMODULE'),
  ('encodings.rot_13',
   'D:\\ENVS\\Python\\Python313\\Lib\\encodings\\rot_13.py',
   'PYMODULE'),
  ('encodings.raw_unicode_escape',
   'D:\\ENVS\\Python\\Python313\\Lib\\encodings\\raw_unicode_escape.py',
   'PYMODULE'),
  ('encodings.quopri_codec',
   'D:\\ENVS\\Python\\Python313\\Lib\\encodings\\quopri_codec.py',
   'PYMODULE'),
  ('encodings.punycode',
   'D:\\ENVS\\Python\\Python313\\Lib\\encodings\\punycode.py',
   'PYMODULE'),
  ('encodings.ptcp154',
   'D:\\ENVS\\Python\\Python313\\Lib\\encodings\\ptcp154.py',
   'PYMODULE'),
  ('encodings.palmos',
   'D:\\ENVS\\Python\\Python313\\Lib\\encodings\\palmos.py',
   'PYMODULE'),
  ('encodings.oem',
   'D:\\ENVS\\Python\\Python313\\Lib\\encodings\\oem.py',
   'PYMODULE'),
  ('encodings.mbcs',
   'D:\\ENVS\\Python\\Python313\\Lib\\encodings\\mbcs.py',
   'PYMODULE'),
  ('encodings.mac_turkish',
   'D:\\ENVS\\Python\\Python313\\Lib\\encodings\\mac_turkish.py',
   'PYMODULE'),
  ('encodings.mac_romanian',
   'D:\\ENVS\\Python\\Python313\\Lib\\encodings\\mac_romanian.py',
   'PYMODULE'),
  ('encodings.mac_roman',
   'D:\\ENVS\\Python\\Python313\\Lib\\encodings\\mac_roman.py',
   'PYMODULE'),
  ('encodings.mac_latin2',
   'D:\\ENVS\\Python\\Python313\\Lib\\encodings\\mac_latin2.py',
   'PYMODULE'),
  ('encodings.mac_iceland',
   'D:\\ENVS\\Python\\Python313\\Lib\\encodings\\mac_iceland.py',
   'PYMODULE'),
  ('encodings.mac_greek',
   'D:\\ENVS\\Python\\Python313\\Lib\\encodings\\mac_greek.py',
   'PYMODULE'),
  ('encodings.mac_farsi',
   'D:\\ENVS\\Python\\Python313\\Lib\\encodings\\mac_farsi.py',
   'PYMODULE'),
  ('encodings.mac_cyrillic',
   'D:\\ENVS\\Python\\Python313\\Lib\\encodings\\mac_cyrillic.py',
   'PYMODULE'),
  ('encodings.mac_croatian',
   'D:\\ENVS\\Python\\Python313\\Lib\\encodings\\mac_croatian.py',
   'PYMODULE'),
  ('encodings.mac_arabic',
   'D:\\ENVS\\Python\\Python313\\Lib\\encodings\\mac_arabic.py',
   'PYMODULE'),
  ('encodings.latin_1',
   'D:\\ENVS\\Python\\Python313\\Lib\\encodings\\latin_1.py',
   'PYMODULE'),
  ('encodings.kz1048',
   'D:\\ENVS\\Python\\Python313\\Lib\\encodings\\kz1048.py',
   'PYMODULE'),
  ('encodings.koi8_u',
   'D:\\ENVS\\Python\\Python313\\Lib\\encodings\\koi8_u.py',
   'PYMODULE'),
  ('encodings.koi8_t',
   'D:\\ENVS\\Python\\Python313\\Lib\\encodings\\koi8_t.py',
   'PYMODULE'),
  ('encodings.koi8_r',
   'D:\\ENVS\\Python\\Python313\\Lib\\encodings\\koi8_r.py',
   'PYMODULE'),
  ('encodings.johab',
   'D:\\ENVS\\Python\\Python313\\Lib\\encodings\\johab.py',
   'PYMODULE'),
  ('encodings.iso8859_9',
   'D:\\ENVS\\Python\\Python313\\Lib\\encodings\\iso8859_9.py',
   'PYMODULE'),
  ('encodings.iso8859_8',
   'D:\\ENVS\\Python\\Python313\\Lib\\encodings\\iso8859_8.py',
   'PYMODULE'),
  ('encodings.iso8859_7',
   'D:\\ENVS\\Python\\Python313\\Lib\\encodings\\iso8859_7.py',
   'PYMODULE'),
  ('encodings.iso8859_6',
   'D:\\ENVS\\Python\\Python313\\Lib\\encodings\\iso8859_6.py',
   'PYMODULE'),
  ('encodings.iso8859_5',
   'D:\\ENVS\\Python\\Python313\\Lib\\encodings\\iso8859_5.py',
   'PYMODULE'),
  ('encodings.iso8859_4',
   'D:\\ENVS\\Python\\Python313\\Lib\\encodings\\iso8859_4.py',
   'PYMODULE'),
  ('encodings.iso8859_3',
   'D:\\ENVS\\Python\\Python313\\Lib\\encodings\\iso8859_3.py',
   'PYMODULE'),
  ('encodings.iso8859_2',
   'D:\\ENVS\\Python\\Python313\\Lib\\encodings\\iso8859_2.py',
   'PYMODULE'),
  ('encodings.iso8859_16',
   'D:\\ENVS\\Python\\Python313\\Lib\\encodings\\iso8859_16.py',
   'PYMODULE'),
  ('encodings.iso8859_15',
   'D:\\ENVS\\Python\\Python313\\Lib\\encodings\\iso8859_15.py',
   'PYMODULE'),
  ('encodings.iso8859_14',
   'D:\\ENVS\\Python\\Python313\\Lib\\encodings\\iso8859_14.py',
   'PYMODULE'),
  ('encodings.iso8859_13',
   'D:\\ENVS\\Python\\Python313\\Lib\\encodings\\iso8859_13.py',
   'PYMODULE'),
  ('encodings.iso8859_11',
   'D:\\ENVS\\Python\\Python313\\Lib\\encodings\\iso8859_11.py',
   'PYMODULE'),
  ('encodings.iso8859_10',
   'D:\\ENVS\\Python\\Python313\\Lib\\encodings\\iso8859_10.py',
   'PYMODULE'),
  ('encodings.iso8859_1',
   'D:\\ENVS\\Python\\Python313\\Lib\\encodings\\iso8859_1.py',
   'PYMODULE'),
  ('encodings.iso2022_kr',
   'D:\\ENVS\\Python\\Python313\\Lib\\encodings\\iso2022_kr.py',
   'PYMODULE'),
  ('encodings.iso2022_jp_ext',
   'D:\\ENVS\\Python\\Python313\\Lib\\encodings\\iso2022_jp_ext.py',
   'PYMODULE'),
  ('encodings.iso2022_jp_3',
   'D:\\ENVS\\Python\\Python313\\Lib\\encodings\\iso2022_jp_3.py',
   'PYMODULE'),
  ('encodings.iso2022_jp_2004',
   'D:\\ENVS\\Python\\Python313\\Lib\\encodings\\iso2022_jp_2004.py',
   'PYMODULE'),
  ('encodings.iso2022_jp_2',
   'D:\\ENVS\\Python\\Python313\\Lib\\encodings\\iso2022_jp_2.py',
   'PYMODULE'),
  ('encodings.iso2022_jp_1',
   'D:\\ENVS\\Python\\Python313\\Lib\\encodings\\iso2022_jp_1.py',
   'PYMODULE'),
  ('encodings.iso2022_jp',
   'D:\\ENVS\\Python\\Python313\\Lib\\encodings\\iso2022_jp.py',
   'PYMODULE'),
  ('encodings.idna',
   'D:\\ENVS\\Python\\Python313\\Lib\\encodings\\idna.py',
   'PYMODULE'),
  ('encodings.hz',
   'D:\\ENVS\\Python\\Python313\\Lib\\encodings\\hz.py',
   'PYMODULE'),
  ('encodings.hp_roman8',
   'D:\\ENVS\\Python\\Python313\\Lib\\encodings\\hp_roman8.py',
   'PYMODULE'),
  ('encodings.hex_codec',
   'D:\\ENVS\\Python\\Python313\\Lib\\encodings\\hex_codec.py',
   'PYMODULE'),
  ('encodings.gbk',
   'D:\\ENVS\\Python\\Python313\\Lib\\encodings\\gbk.py',
   'PYMODULE'),
  ('encodings.gb2312',
   'D:\\ENVS\\Python\\Python313\\Lib\\encodings\\gb2312.py',
   'PYMODULE'),
  ('encodings.gb18030',
   'D:\\ENVS\\Python\\Python313\\Lib\\encodings\\gb18030.py',
   'PYMODULE'),
  ('encodings.euc_kr',
   'D:\\ENVS\\Python\\Python313\\Lib\\encodings\\euc_kr.py',
   'PYMODULE'),
  ('encodings.euc_jp',
   'D:\\ENVS\\Python\\Python313\\Lib\\encodings\\euc_jp.py',
   'PYMODULE'),
  ('encodings.euc_jisx0213',
   'D:\\ENVS\\Python\\Python313\\Lib\\encodings\\euc_jisx0213.py',
   'PYMODULE'),
  ('encodings.euc_jis_2004',
   'D:\\ENVS\\Python\\Python313\\Lib\\encodings\\euc_jis_2004.py',
   'PYMODULE'),
  ('encodings.cp950',
   'D:\\ENVS\\Python\\Python313\\Lib\\encodings\\cp950.py',
   'PYMODULE'),
  ('encodings.cp949',
   'D:\\ENVS\\Python\\Python313\\Lib\\encodings\\cp949.py',
   'PYMODULE'),
  ('encodings.cp932',
   'D:\\ENVS\\Python\\Python313\\Lib\\encodings\\cp932.py',
   'PYMODULE'),
  ('encodings.cp875',
   'D:\\ENVS\\Python\\Python313\\Lib\\encodings\\cp875.py',
   'PYMODULE'),
  ('encodings.cp874',
   'D:\\ENVS\\Python\\Python313\\Lib\\encodings\\cp874.py',
   'PYMODULE'),
  ('encodings.cp869',
   'D:\\ENVS\\Python\\Python313\\Lib\\encodings\\cp869.py',
   'PYMODULE'),
  ('encodings.cp866',
   'D:\\ENVS\\Python\\Python313\\Lib\\encodings\\cp866.py',
   'PYMODULE'),
  ('encodings.cp865',
   'D:\\ENVS\\Python\\Python313\\Lib\\encodings\\cp865.py',
   'PYMODULE'),
  ('encodings.cp864',
   'D:\\ENVS\\Python\\Python313\\Lib\\encodings\\cp864.py',
   'PYMODULE'),
  ('encodings.cp863',
   'D:\\ENVS\\Python\\Python313\\Lib\\encodings\\cp863.py',
   'PYMODULE'),
  ('encodings.cp862',
   'D:\\ENVS\\Python\\Python313\\Lib\\encodings\\cp862.py',
   'PYMODULE'),
  ('encodings.cp861',
   'D:\\ENVS\\Python\\Python313\\Lib\\encodings\\cp861.py',
   'PYMODULE'),
  ('encodings.cp860',
   'D:\\ENVS\\Python\\Python313\\Lib\\encodings\\cp860.py',
   'PYMODULE'),
  ('encodings.cp858',
   'D:\\ENVS\\Python\\Python313\\Lib\\encodings\\cp858.py',
   'PYMODULE'),
  ('encodings.cp857',
   'D:\\ENVS\\Python\\Python313\\Lib\\encodings\\cp857.py',
   'PYMODULE'),
  ('encodings.cp856',
   'D:\\ENVS\\Python\\Python313\\Lib\\encodings\\cp856.py',
   'PYMODULE'),
  ('encodings.cp855',
   'D:\\ENVS\\Python\\Python313\\Lib\\encodings\\cp855.py',
   'PYMODULE'),
  ('encodings.cp852',
   'D:\\ENVS\\Python\\Python313\\Lib\\encodings\\cp852.py',
   'PYMODULE'),
  ('encodings.cp850',
   'D:\\ENVS\\Python\\Python313\\Lib\\encodings\\cp850.py',
   'PYMODULE'),
  ('encodings.cp775',
   'D:\\ENVS\\Python\\Python313\\Lib\\encodings\\cp775.py',
   'PYMODULE'),
  ('encodings.cp737',
   'D:\\ENVS\\Python\\Python313\\Lib\\encodings\\cp737.py',
   'PYMODULE'),
  ('encodings.cp720',
   'D:\\ENVS\\Python\\Python313\\Lib\\encodings\\cp720.py',
   'PYMODULE'),
  ('encodings.cp500',
   'D:\\ENVS\\Python\\Python313\\Lib\\encodings\\cp500.py',
   'PYMODULE'),
  ('encodings.cp437',
   'D:\\ENVS\\Python\\Python313\\Lib\\encodings\\cp437.py',
   'PYMODULE'),
  ('encodings.cp424',
   'D:\\ENVS\\Python\\Python313\\Lib\\encodings\\cp424.py',
   'PYMODULE'),
  ('encodings.cp273',
   'D:\\ENVS\\Python\\Python313\\Lib\\encodings\\cp273.py',
   'PYMODULE'),
  ('encodings.cp1258',
   'D:\\ENVS\\Python\\Python313\\Lib\\encodings\\cp1258.py',
   'PYMODULE'),
  ('encodings.cp1257',
   'D:\\ENVS\\Python\\Python313\\Lib\\encodings\\cp1257.py',
   'PYMODULE'),
  ('encodings.cp1256',
   'D:\\ENVS\\Python\\Python313\\Lib\\encodings\\cp1256.py',
   'PYMODULE'),
  ('encodings.cp1255',
   'D:\\ENVS\\Python\\Python313\\Lib\\encodings\\cp1255.py',
   'PYMODULE'),
  ('encodings.cp1254',
   'D:\\ENVS\\Python\\Python313\\Lib\\encodings\\cp1254.py',
   'PYMODULE'),
  ('encodings.cp1253',
   'D:\\ENVS\\Python\\Python313\\Lib\\encodings\\cp1253.py',
   'PYMODULE'),
  ('encodings.cp1252',
   'D:\\ENVS\\Python\\Python313\\Lib\\encodings\\cp1252.py',
   'PYMODULE'),
  ('encodings.cp1251',
   'D:\\ENVS\\Python\\Python313\\Lib\\encodings\\cp1251.py',
   'PYMODULE'),
  ('encodings.cp1250',
   'D:\\ENVS\\Python\\Python313\\Lib\\encodings\\cp1250.py',
   'PYMODULE'),
  ('encodings.cp1140',
   'D:\\ENVS\\Python\\Python313\\Lib\\encodings\\cp1140.py',
   'PYMODULE'),
  ('encodings.cp1125',
   'D:\\ENVS\\Python\\Python313\\Lib\\encodings\\cp1125.py',
   'PYMODULE'),
  ('encodings.cp1026',
   'D:\\ENVS\\Python\\Python313\\Lib\\encodings\\cp1026.py',
   'PYMODULE'),
  ('encodings.cp1006',
   'D:\\ENVS\\Python\\Python313\\Lib\\encodings\\cp1006.py',
   'PYMODULE'),
  ('encodings.cp037',
   'D:\\ENVS\\Python\\Python313\\Lib\\encodings\\cp037.py',
   'PYMODULE'),
  ('encodings.charmap',
   'D:\\ENVS\\Python\\Python313\\Lib\\encodings\\charmap.py',
   'PYMODULE'),
  ('encodings.bz2_codec',
   'D:\\ENVS\\Python\\Python313\\Lib\\encodings\\bz2_codec.py',
   'PYMODULE'),
  ('encodings.big5hkscs',
   'D:\\ENVS\\Python\\Python313\\Lib\\encodings\\big5hkscs.py',
   'PYMODULE'),
  ('encodings.big5',
   'D:\\ENVS\\Python\\Python313\\Lib\\encodings\\big5.py',
   'PYMODULE'),
  ('encodings.base64_codec',
   'D:\\ENVS\\Python\\Python313\\Lib\\encodings\\base64_codec.py',
   'PYMODULE'),
  ('encodings.ascii',
   'D:\\ENVS\\Python\\Python313\\Lib\\encodings\\ascii.py',
   'PYMODULE'),
  ('encodings.aliases',
   'D:\\ENVS\\Python\\Python313\\Lib\\encodings\\aliases.py',
   'PYMODULE'),
  ('encodings',
   'D:\\ENVS\\Python\\Python313\\Lib\\encodings\\__init__.py',
   'PYMODULE'),
  ('weakref', 'D:\\ENVS\\Python\\Python313\\Lib\\weakref.py', 'PYMODULE'),
  ('traceback', 'D:\\ENVS\\Python\\Python313\\Lib\\traceback.py', 'PYMODULE'),
  ('warnings', 'D:\\ENVS\\Python\\Python313\\Lib\\warnings.py', 'PYMODULE'),
  ('functools', 'D:\\ENVS\\Python\\Python313\\Lib\\functools.py', 'PYMODULE'),
  ('reprlib', 'D:\\ENVS\\Python\\Python313\\Lib\\reprlib.py', 'PYMODULE'),
  ('os', 'D:\\ENVS\\Python\\Python313\\Lib\\os.py', 'PYMODULE')])
