from business.schema.baseSchema import BaseSchema

# dict(idnum="Long", data={"action": "buy", "contracts": "0.02", "position_size": "0.02"}, price="3365.130",
#      signal_param="{}", signal_type="4e6f921e-1a41-48cd-8148-4e0d2eb43186", symbol="XAUUSD",
#      time="2025-08-25T08:20:00Z")

# {"idnum":"Long","data":{"action":"buy","contracts":"0.02","position_size":"0.02"},"price":"3365.130","signal_param":"{}","signal_type":"4e6f921e-1a41-48cd-8148-4e0d2eb43186","symbol":"XAUUSD","time":"2025-08-25T08:20:00Z"}

class SignalData(BaseSchema):
    action:str='',
    contracts:float=0.00,
    position_size:float=-0.00

    class Config:
        json_schema_extra  = {
            "example": {
                "action":"buy",
                "contracts":"0.02",
                "position_size":"0.02"
            }
        }

class Signal(BaseSchema):
    idnum:str = ''
    platform:str= ''
    comment:str= ''
    data:SignalData=SignalData()
    price:float=0.00,
    signal_param:str='{}',
    signal_type:str='',
    symbol:str='',
    time:str=''

    class Config:
        json_schema_extra  = {
            "example": {
                "idnum":"Long",
                "comment":"vp213A",
                "platform":"mt5",
                "data":{"action":"buy","contracts":"0.02","position_size":"0.02"},
                "price":"3365.130",
                "signal_param":"{}",
                "signal_type":"4e6f921e-1a41-48cd-8148-4e0d2eb43186",
                "symbol":"XAUUSD","time":"2025-08-25T08:20:00Z"
            }
        }



