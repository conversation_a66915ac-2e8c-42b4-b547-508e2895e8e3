
from pydantic import BaseModel

from business.schema.baseSchema import UserBaseSchema, BaseSchema


class AuthDataSchema(BaseModel):
    code: int = 0
    message: str = 'SUCCESS'
    token: str = None
    user: UserBaseSchema = None

class LoginType:
    Token:str='token'
    Password:str = 'password'

class LoginInputSchema(BaseModel):
    type: str
    login_name: str
    token: str
    password: str = None

class UserSchema(BaseSchema):
    login_name: str
    username : str
    password : str
