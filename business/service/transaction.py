import asyncio
import MetaT<PERSON>r<PERSON> as mt5

from business.dao.emailDao import <PERSON>ailDao
from business.models.email import Email
from business.schema.baseSchema import ArgsSchema, FilterSchema
from business.schema.signal import Signal
from core.common.config import Config, ServiceStatus
from core.common.utils.convert import json2obj
from core.common.utils.logHelper import logger
from core.platform.helper import mt5Helper


async def trans_task(delay:int=10):
    logger.info("服务启动，开始处理交易...")
    while Config.service.status in [ServiceStatus.Starting, ServiceStatus.Running]:
        task = asyncio.create_task(transaction())
        await asyncio.sleep(delay)
        await task
    Config.service.status = ServiceStatus.Stoped
    logger.info("服务停止，交易处理结束...")

def get_Emails()->list[Email]:
    emails = []
    try:
        dao = EmailDao()
        args = ArgsSchema()
        args.is_deleted = '0'
        args.size = 200
        args.filters = []
        args.filters.append(FilterSchema(key='is_transaction', condition='=', value='0'))
        args.filters.append(FilterSchema(key='subject', condition='like', value='XAU'))
        result = dao.read_list(args)
        logger.debug(f'交易数量:{len(result.list)}')
        for mail in result.list:
            email = Email(**mail)
            emails.append(email)
            # logger.debug(email.content_text)
            # try:
            #     _ = json2obj(email.content_text,Signal)
            #     emails.append(email)
            # except Exception as ex:
            #     # 无法解析标记删除
            #     email.is_deleted = 1
            #     dao.update(email)
            #     logger.error(ex)
    except Exception as ex1:
        logger.error(ex1)
    return emails

def mt5_trans(signal:Signal):
    logger.debug(f'处理交易:{signal}')
    # 判断交易类型
    # 平仓
    result = None
    if float(signal.data.position_size) == 0:
       positions = mt5Helper.get_open_positions(signal.symbol)
       positions = [p for p in positions if p.comment == signal.comment in positions]
       logger.warning(f"查询到[{len(positions)}]条持仓, 即将平仓.")
       for position in positions:
           result = mt5Helper.close_position(position=position)
           # 保存当前持仓收益
    else:
        if signal.data.action in ("buy", 'sell'):
            logger.debug('开仓')
            order_type = mt5.ORDER_TYPE_BUY if signal.data.action == 'buy' else mt5.ORDER_TYPE_SELL
            result = mt5Helper.open_order(signal.symbol, 1.00, order_type,comment=signal.comment)
            # 保存开仓信息

        else:
            logger.error('无效的action.')
    return result

async def transaction():
    emails = get_Emails()
    logger.info(f'需要处理交易的邮件数量:{len(emails)}')
    for email in emails:
        signal = Signal()
        try:
            signal = json2obj(email.content_text,Signal)
        except Exception as ex:
            # signal = None
            email.is_deleted = 1
            EmailDao().update(email)
            logger.error(ex)
        match signal.platform:
            case 'mt5':
                logger.debug('处理MT5信号')
            case 'mt4':
                logger.debug('处理MT4信号')
            case 'oke':
                logger.debug('处理OKE信号')
            case '':
                # 处理空平台 使用MT5
                logger.debug('处理MT5信号')
                mt5_trans(signal)
            case _:
                logger.debug('未知平台')

if __name__ == '__main__':
    asyncio.run(transaction())