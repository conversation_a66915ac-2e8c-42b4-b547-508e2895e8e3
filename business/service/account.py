import hashlib
import uuid

from business.dao.accountDao import Account<PERSON>ao
from business.models.account import Account
from business.models.user import User
from business.schema.account import AccountSchema
from business.schema.baseSchema import ArgsSchema
from business.service.base import BaseService
from core.common.utils.convert import json2obj, obj2json
from core.common.utils.logHelper import logger


class AccountService(BaseService):
    def __init__(self, auth_data: dict = None):
        auth_data = dict() if not auth_data else auth_data
        user_id = auth_data.get('user_id', 0)
        self.Model = Account
        self.dao = AccountDao()
        self.dao.Model = User
        # self.redis = RedisUtils()


        super().__init__(user_id, auth_data)

    def create(self, account:AccountSchema):
        if not account:
            return {'code': 30000, 'message': '账户信息不能为空'}
        elif not account.apikey:
            return {'code': 30001, 'message': 'apikey不能为空'}
        elif not account.secretkey:
            return {'code': 30002, 'message': 'secretkey不能为空'}
        elif not account.remark:
            return {'code': 30003, 'message': '备注不能为空'}
        # u = self.dao.read_by_login_name(user.login_name)
        # if u:
        #     return {'code': 20004, 'message': f'登录名[{u.login_name}]已存在'}
        return super().create(account)

    def read_list(self,args:ArgsSchema):
        logger.debug(args)
        return super().read_list(args)

