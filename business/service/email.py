import asyncio

from business.dao.emailDao import EmailDao
from business.models.email import Email
from core.common.config import Config, ServiceStatus
from core.common.emailHelper import EmailManager
from core.common.utils.logHelper import logger


async def email_task(delay:int=5):
    logger.info("服务启动，开始接收邮件...")
    while Config.service.status in [ServiceStatus.Starting, ServiceStatus.Running]:
        task = asyncio.create_task(receive_emails())
        await asyncio.sleep(delay)
        await task
    Config.service.status = ServiceStatus.Stoped
    logger.info("服务停止，邮件接收结束...")

# 接受邮件
async def receive_emails():
    try:
        # 初始化管理器
        email_manager = EmailManager(Config.email.email, Config.email.auth_code)
        emails = email_manager.receive_emails(
            mailbox="INBOX",  # 收件箱
            limit=100,
            criteria="UNSEEN"  # 只获取未读
        )
        logger.info(f'接收到【{len(emails)}】封邮件。')
        for mail in emails:
            logger.info(f'正在处理邮件:{mail}｝')
            # 保存邮件到数据库
            dao = EmailDao()
            email = Email(**mail)
            logger.debug(email)
            dao.create(email)
            logger.info(f'邮件已保存到数据库:{mail}｝')
            email_manager.mark_as_read(email.id)
            logger.info(f'邮件已标记为【已读】:{mail}｝')
    except Exception as ex:
        logger.error(ex)

