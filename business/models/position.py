from sqlalchemy import Column, String, text, BOOLEAN, INT, Float

from core.db.modelBase import ModelBase


# TradePosition(ticket=53062627379, time=1756287385, time_msc=1756287385981, time_update=1756287385, time_update_msc=1756287385981, type=0, magic=0, identifier=53062627379, reason=0, volume=1.0, price_open=3380.15, sl=0.0, tp=0.0, price_current=3379.87, swap=0.0, profit=-28.0, symbol='XAUUSD', comment='', external_id='')
# {'ticket': 53062627379, 'time': 1756287385, 'time_msc': 1756287385981, 'time_update': 1756287385, 'time_update_msc': 1756287385981, 'type': 0, 'magic': 0, 'identifier': 53062627379, 'reason': 0, 'volume': 1.0, 'price_open': 3380.15, 'sl': 0.0, 'tp': 0.0, 'price_current': 3379.87, 'swap': 0.0, 'profit': -28.0, 'symbol': 'XAUUSD', 'comment': '', 'external_id': ''}
class Position(ModelBase):
    __tablename__ = 'position'
    __table_args__ = {'comment': '持仓信息'}

    ticket = Column(INT, nullable=False, default=0, comment='订单号')
    # ticket=53062627379,
    # time=1756287385,
    time = Column(INT, nullable=False, default=0, comment='时间')
    # time_msc=1756287385981,
    time_msc = Column(INT, nullable=False, default=0, comment='时间毫秒')
    # time_update=1756287385,
    time_update = Column(INT, nullable=False, default=0, comment='更新时间')
    # time_update_msc=1756287385981,
    time_update_msc = Column(INT, nullable=False, default=0, comment='更新时间毫秒')
    # type=0,
    type = Column(INT, nullable=False, default=0, comment='类型')
    # magic=0,
    magic = Column(INT, nullable=False, default=0, comment='魔术数')
    # identifier=53062627379,
    identifier = Column(INT, nullable=False, default=0, comment='标识符')
    # reason=0,
    reason = Column(INT, nullable=False, default=0, comment='原因')
    # volume=1.0,
    volume = Column(Float, nullable=False, default=0.00, comment='手数')
    # price_open=3380.15,
    price_open = Column(Float, nullable=False, default=0.00, comment='开仓价')
    # sl=0.0,
    sl = Column(Float, nullable=False, default=0.00, comment='止损')
    # tp=0.0,
    tp = Column(Float, nullable=False, default=0.00, comment='止盈')
    # price_current=3379.87,
    price_current = Column(Float, nullable=False, default=0.00, comment='当前价')
    # swap=0.0,
    swap = Column(Float, nullable=False, default=0.00, comment='.swap')
    # profit=-28.0,
    profit = Column(Float, nullable=False, default=0.00, comment='利润')
    # symbol='XAUUSD',
    symbol = Column(String(255), nullable=False, server_default=text("''"), comment='品种')
    # comment='',
    comment = Column(String(255), nullable=False, server_default=text("''"), comment='评论')
    # external_id=''
    external_id = Column(String(255), nullable=False, server_default=text("''"), comment='外部id')
