from sqlalchemy import Column, String, text, BOOLEAN, INT

from core.db.modelBase import ModelBase


# TradePosition(ticket=53062627379, time=1756287385, time_msc=1756287385981, time_update=1756287385, time_update_msc=1756287385981, type=0, magic=0, identifier=53062627379, reason=0, volume=1.0, price_open=3380.15, sl=0.0, tp=0.0, price_current=3379.87, swap=0.0, profit=-28.0, symbol='XAUUSD', comment='', external_id='')
# {'ticket': 53062627379, 'time': 1756287385, 'time_msc': 1756287385981, 'time_update': 1756287385, 'time_update_msc': 1756287385981, 'type': 0, 'magic': 0, 'identifier': 53062627379, 'reason': 0, 'volume': 1.0, 'price_open': 3380.15, 'sl': 0.0, 'tp': 0.0, 'price_current': 3379.87, 'swap': 0.0, 'profit': -28.0, 'symbol': 'XAUUSD', 'comment': '', 'external_id': ''}
class Position(ModelBase):
    __tablename__ = 'position'
    __table_args__ = {'comment': '持仓信息'}

    ticket = Column(INT, nullable=False, default=0, comment='订单号')
    # ticket=53062627379,
    # time=1756287385,
    # time_msc=1756287385981,
    # time_update=1756287385,
    # time_update_msc=1756287385981,
    # type=0,
    # magic=0,
    # identifier=53062627379,
    # reason=0,
    # volume=1.0,
    # price_open=3380.15,
    # sl=0.0,
    # tp=0.0,
    # price_current=3379.87,
    # swap=0.0,
    # profit=-28.0,
    # symbol='XAUUSD',
    # comment='',
    # external_id=''
