from sqlalchemy import Column, String, text

from core.db.modelBase import ModelBase

# apikey = "d0b6734c-05d4-4f3c-86f7-42d3501f3e59"
# secretkey = "17808722CA0B4677C964AC09349608FA"
# IP = ""
# 备注名 = "测试模拟交易"
# 权限 = "读取/交易"
class Account(ModelBase):
    __tablename__ = 'account'
    __table_args__ = {'comment': '账户'}

    apikey = Column(String(255), unique=True, nullable=False, server_default=text("''"), comment='APIKey')
    secretkey = Column(String(255), nullable=False, server_default=text("''"), comment='SecretKey')
    ip = Column(String(255), nullable=False, server_default=text("''"), comment='IP')
    role = Column(String(255), nullable=False, server_default=text("''"), comment='权限')
    remark = Column(String(255), nullable=False, server_default=text("''"), comment='备注')
