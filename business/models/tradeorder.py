from sqlalchemy import Column, String, text, BOOLEAN, INT, Float

from core.db.modelBase import ModelBase


# TradeOrder(ticket=53055772556, time_setup=1756212985, time_setup_msc=1756212985946, time_done=1756212985, time_done_msc=1756212985948, time_expiration=0, type=1, type_time=0, type_filling=1, state=4, magic=0, position_id=53055587012, position_by_id=0, reason=5, volume_initial=0.01, volume_current=0.0, price_open=1.16418, sl=0.0, tp=0.0, price_current=1.16418, price_stoplimit=0.0, symbol='EURUSD', comment='[tp 1.16418]', external_id='')
# {'ticket': 53055772556, 'time_setup': 1756212985, 'time_setup_msc': 1756212985946, 'time_done': 1756212985, 'time_done_msc': 1756212985948, 'time_expiration': 0, 'type': 1, 'type_time': 0, 'type_filling': 1, 'state': 4, 'magic': 0, 'position_id': 53055587012, 'position_by_id': 0, 'reason': 5, 'volume_initial': 0.01, 'volume_current': 0.0, 'price_open': 1.16418, 'sl': 0.0, 'tp': 0.0, 'price_current': 1.16418, 'price_stoplimit': 0.0, 'symbol': 'EURUSD', 'comment': '[tp 1.16418]', 'external_id': ''}
class TradeOrder(ModelBase):
    __tablename__ = 'trade_order'
    __table_args__ = {'comment': '交易订单信息'}

    # ticket=53055772556,
    ticket = Column(INT, nullable=False, default=0, comment='订单号')
    # time_setup=1756212985,
    time_setup = Column(INT, nullable=False, default=0, comment='创建时间')
    # time_setup_msc=1756212985946,
    time_setup_msc = Column(INT, nullable=False, default=0, comment='创建时间毫秒')
    # time_done=1756212985,
    time_done = Column(INT, nullable=False, default=0, comment='完成时间')
    # time_done_msc=1756212985948,
    time_done_msc = Column(INT, nullable=False, default=0, comment='完成时间毫秒')
    # time_expiration=0,
    time_expiration = Column(INT, nullable=False, default=0, comment='过期时间')
    # type=1,
    type = Column(INT, nullable=False, default=0, comment='订单类型')
    # type_time=0,
    type_time = Column(INT, nullable=False, default=0, comment='时间类型')
    # type_filling=1,
    type_filling = Column(INT, nullable=False, default=0, comment='填充类型')
    # state=4,
    state = Column(INT, nullable=False, default=0, comment='订单状态')
    # magic=0,
    magic = Column(INT, nullable=False, default=0, comment='魔术数')
    # position_id=53055587012,
    position_id = Column(INT, nullable=False, default=0, comment='持仓ID')
    # position_by_id=0,
    position_by_id = Column(INT, nullable=False, default=0, comment='相对持仓ID')
    # reason=5,
    reason = Column(INT, nullable=False, default=0, comment='原因')
    # volume_initial=0.01,
    volume_initial = Column(Float, nullable=False, default=0.00, comment='初始手数')
    # volume_current=0.0,
    volume_current = Column(Float, nullable=False, default=0.00, comment='当前手数')
    # price_open=1.16418,
    price_open = Column(Float, nullable=False, default=0.00, comment='开仓价')
    # sl=0.0,
    sl = Column(Float, nullable=False, default=0.00, comment='止损')
    # tp=0.0,
    tp = Column(Float, nullable=False, default=0.00, comment='止盈')
    # price_current=1.16418,
    price_current = Column(Float, nullable=False, default=0.00, comment='当前价')
    # price_stoplimit=0.0,
    price_stoplimit = Column(Float, nullable=False, default=0.00, comment='止损限价')
    # symbol='EURUSD',
    symbol = Column(String(255), nullable=False, server_default=text("''"), comment='交易品种')
    # comment='[tp 1.16418]',
    comment = Column(String(255), nullable=False, server_default=text("''"), comment='评论')
    # external_id=''
    external_id = Column(String(255), nullable=False, server_default=text("''"), comment='外部ID')
