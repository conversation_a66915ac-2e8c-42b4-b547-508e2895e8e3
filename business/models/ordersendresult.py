from sqlalchemy import Column, String, text, BOOLEAN, INT, Float

from core.db.modelBase import ModelBase


# OrderSendResult(retcode=10009, deal=53012178641, order=53063492391, volume=1.0, price=3384.92, bid=0.0, ask=0.0, comment='Request executed', request_id=4289163617, retcode_external=0, request=TradeRequest(action=1, magic=0, order=0, symbol='XAUUSD', volume=1.0, price=3384.9, stoplimit=0.0, sl=0.0, tp=0.0, deviation=10, type=0, type_filling=0, type_time=0, expiration=0, comment='', position=0, position_by=0))
class OrderSendResult(ModelBase):
    __tablename__ = 'order_send_result'
    __table_args__ = {'comment': '订单发送结果信息'}

    # retcode=10009,
    retcode = Column(INT, nullable=False, default=0, comment='返回代码')
    # deal=53012178641,
    deal = Column(INT, nullable=False, default=0, comment='成交号')
    # order=53063492391,
    order = Column(INT, nullable=False, default=0, comment='订单号')
    # volume=1.0,
    volume = Column(Float, nullable=False, default=0.00, comment='成交手数')
    # price=3384.92,
    price = Column(Float, nullable=False, default=0.00, comment='成交价格')
    # bid=0.0,
    bid = Column(Float, nullable=False, default=0.00, comment='买价')
    # ask=0.0,
    ask = Column(Float, nullable=False, default=0.00, comment='卖价')
    # comment='Request executed',
    comment = Column(String(255), nullable=False, server_default=text("''"), comment='执行结果说明')
    # request_id=4289163617,
    request_id = Column(INT, nullable=False, default=0, comment='请求ID')
    # retcode_external=0,
    retcode_external = Column(INT, nullable=False, default=0, comment='外部返回代码')
    
    # TradeRequest 相关字段
    # action=1,
    request_action = Column(INT, nullable=False, default=0, comment='请求动作')
    # magic=0,
    request_magic = Column(INT, nullable=False, default=0, comment='请求魔术数')
    # order=0,
    request_order = Column(INT, nullable=False, default=0, comment='请求订单号')
    # symbol='XAUUSD',
    request_symbol = Column(String(255), nullable=False, server_default=text("''"), comment='请求交易品种')
    # volume=1.0,
    request_volume = Column(Float, nullable=False, default=0.00, comment='请求手数')
    # price=3384.9,
    request_price = Column(Float, nullable=False, default=0.00, comment='请求价格')
    # stoplimit=0.0,
    request_stoplimit = Column(Float, nullable=False, default=0.00, comment='请求止损限价')
    # sl=0.0,
    request_sl = Column(Float, nullable=False, default=0.00, comment='请求止损')
    # tp=0.0,
    request_tp = Column(Float, nullable=False, default=0.00, comment='请求止盈')
    # deviation=10,
    request_deviation = Column(INT, nullable=False, default=0, comment='请求偏差')
    # type=0,
    request_type = Column(INT, nullable=False, default=0, comment='请求类型')
    # type_filling=0,
    request_type_filling = Column(INT, nullable=False, default=0, comment='请求填充类型')
    # type_time=0,
    request_type_time = Column(INT, nullable=False, default=0, comment='请求时间类型')
    # expiration=0,
    request_expiration = Column(INT, nullable=False, default=0, comment='请求过期时间')
    # comment='',
    request_comment = Column(String(255), nullable=False, server_default=text("''"), comment='请求评论')
    # position=0,
    request_position = Column(INT, nullable=False, default=0, comment='请求持仓')
    # position_by=0
    request_position_by = Column(INT, nullable=False, default=0, comment='请求相对持仓')
