from sqlalchemy import Column, String, text, BOOLEAN

from core.db.modelBase import ModelBase


class Email(ModelBase):
    __tablename__ = 'email'
    __table_args__ = {'comment': '邮件'}

    subject = Column(String(255), nullable=False, server_default=text("''"), comment='标题')
    mail_from = Column(String(255), nullable=False, server_default=text("''"), comment='发送者')
    mail_to = Column(String(255), nullable=False, server_default=text("''"), comment='接收者')
    date = Column(String(255), nullable=False, server_default=text("''"), comment='邮件时间')
    content_text = Column(String(255), nullable=True, server_default=text("''"), comment='邮件文字内容')
    content_html = Column(String(255), nullable=True, server_default=text("''"), comment='邮件HTML内容')
    is_read = Column(String(255), nullable=True, server_default=text("''"), comment='是否已读')
    is_transaction = Column(BOOLEAN, nullable=False, default=False, comment='是否已处理')
    attachments = []