from business.models.ordersendresult import OrderSendResult

from business.schema.baseSchema import ArgsSchema, RespListSchema
from core.db.daoBase import BaseDao


class OrderSendResultDao(BaseDao):
    Model = OrderSendResult
    def read(self, id: int, is_deleted: int = 0) -> OrderSendResult:
        return super().read(id)

    def create(self, model: OrderSendResult):
        super().create(model)

    def update(self, model: OrderSendResult):
        super().update(model)

    def read_list(self, args: ArgsSchema) -> RespListSchema:
        return super().read_list(args)
