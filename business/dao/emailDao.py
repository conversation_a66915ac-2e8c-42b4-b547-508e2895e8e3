from business.models.email import Email
from business.models.user import User
from business.schema.baseSchema import ArgsSchema, RespListSchema
from core.db.daoBase import BaseDao
from core.db.modelBase import ModelBase


class EmailDao(BaseDao):
    Model = Email
    def read(self, id: int, is_deleted: int = 0) -> Email:
        return super().read(id)

        # def read_by_openid(self, openid: str) -> User:
    #     return self.db.sess.query(User).filter(
    #         User.openid == openid,
    #         User.is_deleted == 0,
    #     ).first()

    def create(self, model: Email):
        super().create(model)

    def read_list(self, args: ArgsSchema) -> RespListSchema:
        return super().read_list(args)

