
from business.models.tradeorder import TradeOrder
from business.schema.baseSchema import ArgsSchema, RespListSchema
from core.db.daoBase import BaseDao


class TradeoOrderDao(BaseDao):
    Model = TradeOrder
    def read(self, id: int, is_deleted: int = 0) -> TradeOrder:
        return super().read(id)

        # def read_by_openid(self, openid: str) -> User:
    #     return self.db.sess.query(User).filter(
    #         User.openid == openid,
    #         User.is_deleted == 0,
    #     ).first()

    def create(self, model: TradeOrder):
        super().create(model)

    def update(self, model: TradeOrder):
        super().update(model)

    def read_list(self, args: ArgsSchema) -> RespListSchema:
        return super().read_list(args)
