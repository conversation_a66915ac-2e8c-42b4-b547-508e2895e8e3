
from business.models.position import Position
from business.schema.baseSchema import ArgsSchema, RespListSchema
from core.db.daoBase import BaseDao


class PositionDao(BaseDao):
    Model = Position
    def read(self, id: int, is_deleted: int = 0) -> Position:
        return super().read(id)

        # def read_by_openid(self, openid: str) -> User:
    #     return self.db.sess.query(User).filter(
    #         User.openid == openid,
    #         User.is_deleted == 0,
    #     ).first()

    def create(self, model: Position):
        super().create(model)

    def update(self, model: Position):
        super().update(model)

    def read_list(self, args: ArgsSchema) -> RespListSchema:
        return super().read_list(args)
