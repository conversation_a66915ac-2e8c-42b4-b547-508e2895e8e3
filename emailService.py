import asyncio
import signal

from sqlalchemy import create_engine

from business.service import email, transaction
from core.common.config import Config, ServiceStatus
from core.common.utils.logHelper import logger
from core.db.modelBase import ModelBase


def handle_stop(signum, frame):
    try:
        """处理停止信号，设置停止标志"""
        Config.service.status = ServiceStatus.WaitStop
        logger.info(f"收到停止信号，等待停止服务...")
    except Exception as ex:
        logger.error(f"服务运行出错: {str(ex)}")

async def run():
    try:
        logger.info("服务启动中")
        Config.service.status = ServiceStatus.Running
        # logger.info("启动邮件服务")
        # task_email = asyncio.create_task(email.email_task(Config.service.delay))
        logger.info("启动交易服务")
        task_trans = asyncio.create_task(transaction.trans_task(Config.service.delay))
        # await task_email
        await task_trans
        logger.info("服务已停止")
    except Exception as ex:
        logger.error(f"服务运行出错: {ex}")

# 注册信号处理器，监听停止信号
signal.signal(signal.SIGINT, handle_stop)    # 处理Ctrl+C
signal.signal(signal.SIGTERM, handle_stop)   # 处理终止信号

def on_start():
    logger.info("开始加载资源...")
    engine = create_engine(Config.db.get_url())
    ModelBase.metadata.create_all(engine)
    for table in ModelBase.metadata.tables:
        logger.info(f"数据库表{table}创建成功！")
    logger.info("资源加载完成...")

if __name__ == '__main__':
    # 关键修复：使用asyncio.run()来运行异步协程
    try:
        on_start()
        asyncio.run(run())  # 正确的异步调用方式
    except KeyboardInterrupt:
        logger.info("收到停止信号")
    finally:
        logger.info("服务已停止")
