import asyncio
from contextlib import asynccontextmanager

from sqlalchemy import create_engine

from business.models.email import Email
from business.service import email
from core.common.config import Config, ServiceStatus
from core.common.utils.logHelper import logger
from core.db.modelBase import ModelBase


@asynccontextmanager
async def lifespan(app):
    logger.info("开始加载资源....")
    # 创建目录

    # [可选] 在开发时创建表
    if Config.base.env == "dev":
        engine = create_engine(Config.db.get_url())

    # 创建所有模型对应的表
        email111 = Email
        ModelBase.metadata.create_all(engine)
        for table in ModelBase.metadata.tables:
            logger.info(f"数据库表{table}创建成功！")
    Config.service.status = ServiceStatus.Starting
    logger.info("开始启动邮件服务...")
    task = asyncio.create_task(email.email_task(5))
    logger.info("应用启动，资源已加载。")
    yield
    # 应用关闭时执行
    Config.service.status = ServiceStatus.WaitStop
    await task
    logger.info("应用关闭，资源已释放。")