from fastapi import APIRouter

from app.api.router.base import CustomRoute
from business.models.account import Account
from business.schema.account import AccountSchema
from business.schema.auth import AuthDataSchema, LoginInputSchema, LoginType, UserSchema
from business.schema.baseSchema import ArgsSchema
from business.service.account import AccountService
from business.service.user import UserService

router = APIRouter(route_class=CustomRoute)

@router.post('/create')
async def create(account:AccountSchema):
    return AccountService().create(account)

@router.get(path='/{id}')
async def read(id:int):
    return AccountService().read(id)

@router.get(path='/list')
async def read_list(args:ArgsSchema):
    return AccountService().read_list(args)