from fastapi import APIRouter
from fastapi.params import Header






from app.api.router.base import CustomRoute
from business.schema.auth import AuthDataSchema, LoginInputSchema, LoginType, UserSchema
from business.service.user import UserService

router = APIRouter(route_class=CustomRoute)


@router.post('/login', response_model=AuthDataSchema)
async def login(*, login_input: LoginInputSchema):
    """
    用户登录
    :param login_input: 登录结构
    :return:
    """
    srv = UserService()
    match login_input.type:
        case LoginType.Token:
            return srv.login_by_token(login_input.token)
        case LoginType.Password:
            return srv.login_by_password(login_input.login_name, login_input.password)
        case _:
            return {'code': 500, 'message': '登录错误', }

@router.post('/create')
async def create(user:UserSchema):
    return UserService().create(user)

@router.get(path='/{id}')
async def read(id:int):
    return UserService().read(id)