import asyncio
import json
import os
import threading

import uvicorn
from fastapi import Head<PERSON>
from fastapi_offline import FastAPIOffline
from starlette.middleware.cors import CORSMiddleware
from starlette.requests import Request
from starlette.responses import Response
from starlette.staticfiles import StaticFiles

from app.api.router.base import base_router
from app.api.v1.api_v1 import api_v1_router
from business.service import email
from core.common.anonymous import anonymous_path_list
from core.common.config import Config

from core.common.utils.auth import get_auth_data_by_authorization
from core.common.utils.logHelper import logger
from fastapi.exceptions import RequestValidationError, HTTPException

from core.common.utils.pathUtil import get_app_root_path
from lifespan import lifespan


# apikey = "d0b6734c-05d4-4f3c-86f7-42d3501f3e59"
# secretkey = "17808722CA0B4677C964AC09349608FA"
# IP = ""
# 备注名 = "测试模拟交易"
# 权限 = "读取/交易"

def verify_token(x_token: str = Header(..., description="全局访问令牌（必填）")):
    if x_token != "valid_token":
        raise HTTPException(status_code=403, detail="无效的 Token")
    return x_token


Config.fastapi.lifespan = lifespan
app = FastAPIOffline(**Config.fastapi.__dict__)


@app.exception_handler(RequestValidationError)
async def validation_exception_handler(request, exc):
    # await update_log(request.state.log, Response(json.dumps({
    #     'code': 400,
    #     'message': 'Request Validation Error',
    #     'error_detail': str(exc),
    # }), status_code=400))

    return Response(json.dumps({
        'code': 400,
        'message': 'Request Validation Error',
    }), status_code=400)


@app.exception_handler(Exception)
async def http_exception_handler(request, exc):
    # await update_log(request.state.log, Response(json.dumps({
    #     'code': 500,
    #     'message': 'Internal Server Error',
    #     'error_detail': str(exc),
    # }), status_code=500))

    return Response(json.dumps({
        'code': 500,
        'message': 'Internal Server Error',
    }), status_code=500)


@app.middleware('http')
async def auth_token(req: Request, call_next):
    response_type = 1
    response = Response(json.dumps({
        'code': 401,
        'message': 'Unauthorized',
    }), status_code=401)
    path = req.method + req.url.path

    # 判断是否可匿名访问
    if path in anonymous_path_list or path.find('GET/static/') == 0:
        response_type = 2
        response = await call_next(req)
    else:
        auth_data = get_auth_data_by_authorization(req.headers.get('authorization'), 360000)

        if auth_data:
            response_type = 3
            response = await call_next(req)

    if response_type == 1:
        # logger.error(req.json())
        pass
        # log = await create_log(req)
        # await update_log(log, response)

    return response


# 自定义 OpenAPI 文档路由
@app.get("/openapi.json", include_in_schema=False)
async def get_openapi():
    return app.openapi()


app.add_middleware(
    CORSMiddleware,
    allow_origins=['*'],
    allow_credentials=True,
    allow_methods=['*'],
    allow_headers=['*'],
)

app.include_router(base_router)
app.include_router(api_v1_router, prefix='/v1')
# app.mount('./res', StaticFiles(directory=FastapiConfig.res_path), name='res')
static_dir = os.path.join(get_app_root_path(), "static")
app.mount("/static", StaticFiles(directory=static_dir), name="static")

if __name__ == '__main__':
    logger.info('服务开始启动...')
    uvicorn.run(app, host=Config.fastapi.host, port=Config.fastapi.port, log_config=None)
    # uvicorn.run(app, host="127.0.0.1", port=8001)
    logger.info('服务开始已停止。')
    pass
